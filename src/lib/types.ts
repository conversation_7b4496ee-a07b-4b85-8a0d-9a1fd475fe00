// Chat System Types for Phase 1 Integration

export interface Message {
  id: string;
  content: string;
  role: 'user' | 'assistant';
  timestamp: Date;
  canvas?: CanvasContent;
}

export interface CanvasContent {
  type: 'mealPlan' | 'recipe' | 'shoppingList' | 'infoBox';
  data: any;
}

export interface ChatRequest {
  message: string;
  sessionId?: string;
}

export interface ChatResponse {
  text: string;
  canvas?: CanvasContent;
  sessionId: string;
  error?: string;
}

// Phase 1 Canvas Data Structures (placeholders for Phase 3)
export interface MealPlanCanvasData {
  placeholder: boolean;
  text: string;
  message: string;
}

export interface RecipeCanvasData {
  placeholder: boolean;
  text: string;
  message: string;
}

export interface ShoppingListCanvasData {
  placeholder: boolean;
  text: string;
  message: string;
}

export interface InfoBoxCanvasData {
  placeholder: boolean;
  text: string;
  message: string;
}

// Future Phase Types (for reference)
export interface FamilyMember {
  id: string;
  name: string;
  ageGroup: 'child' | 'teen' | 'adult' | 'senior';
  relationship: string;
  dietaryProfile: {
    allergies: string[];
    dietaryType?: string;
    preferences: Array<{
      type: string;
      value: string;
      confidence: number;
      learnedFrom: string;
    }>;
    cookingSkill: 'beginner' | 'intermediate' | 'advanced';
    timeConstraints: 'quick' | 'normal' | 'elaborate';
  };
}

export interface FamilyContext {
  familyId: string;
  activeMembers: FamilyMember[];
  preferences: string[];
} 