import Dexie, { Table } from 'dexie';

export interface AgentProfile {
  id: string; // alltid 'default' i detta läge
  familySize: number;
  allergies?: string[];
  preferences?: string[];
}

class AgentMemoryDB extends <PERSON>ie {
  profiles!: Table<AgentProfile, string>;

  constructor() {
    super('AgentMemoryDB');
    this.version(1).stores({
      profiles: 'id',
    });
  }
}

const db = new AgentMemoryDB();

// Hämta familjeprofil (om den finns)
export async function getProfile(): Promise<AgentProfile | null> {
  return (await db.profiles.get('default')) ?? null;
}

// Spara eller uppdatera profil
export async function setProfile(profile: AgentProfile): Promise<void> {
  await db.profiles.put({ ...profile, id: 'default' });
}
