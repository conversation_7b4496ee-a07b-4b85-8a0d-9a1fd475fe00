import sql from 'mssql';

// Azure SQL Database configuration
const config: sql.config = {
  server: process.env.AZURE_SQL_SERVER!,
  database: process.env.AZURE_SQL_DATABASE!,
  user: process.env.AZURE_SQL_USERNAME!,
  password: process.env.AZURE_SQL_PASSWORD!,
  port: parseInt(process.env.AZURE_SQL_PORT || '1433'),
  options: {
    encrypt: true, // Use encryption for Azure SQL Database
    trustServerCertificate: false, // Don't trust self-signed certificates
    enableArithAbort: true,
  },
  pool: {
    max: 10,
    min: 0,
    idleTimeoutMillis: 30000,
  },
};

// Connection pool
let pool: sql.ConnectionPool | null = null;

/**
 * Get or create a connection pool to Azure SQL Database
 */
export async function getPool(): Promise<sql.ConnectionPool> {
  if (!pool) {
    pool = new sql.ConnectionPool(config);
    await pool.connect();
  }
  return pool;
}

/**
 * Execute a SQL query with parameters
 */
export async function executeQuery<T = any>(
  query: string,
  params?: Record<string, any>
): Promise<sql.IResult<T>> {
  const pool = await getPool();
  const request = pool.request();
  
  // Add parameters if provided
  if (params) {
    Object.entries(params).forEach(([key, value]) => {
      request.input(key, value);
    });
  }
  
  return request.query(query);
}

/**
 * Close the database connection pool
 */
export async function closePool(): Promise<void> {
  if (pool) {
    await pool.close();
    pool = null;
  }
}

// User-related database operations
export interface User {
  id: string;
  email: string | null;
  name: string | null;
  full_name: string | null;
  avatar_url: string | null;
  user_id: string | null;
  token_identifier: string;
  created_at: Date;
  updated_at: Date | null;
}

/**
 * Get user by ID
 */
export async function getUserById(id: string): Promise<User | null> {
  try {
    const result = await executeQuery<User>(
      'SELECT * FROM users WHERE id = @id',
      { id }
    );
    return result.recordset[0] || null;
  } catch (error) {
    console.error('Error getting user by ID:', error);
    throw error;
  }
}

/**
 * Get user by email
 */
export async function getUserByEmail(email: string): Promise<User | null> {
  try {
    const result = await executeQuery<User>(
      'SELECT * FROM users WHERE email = @email',
      { email }
    );
    return result.recordset[0] || null;
  } catch (error) {
    console.error('Error getting user by email:', error);
    throw error;
  }
}

/**
 * Create or update user
 */
export async function upsertUser(user: Omit<User, 'created_at' | 'updated_at'>): Promise<User> {
  try {
    const now = new Date();
    const result = await executeQuery<User>(
      `
      MERGE users AS target
      USING (SELECT @id as id) AS source
      ON target.id = source.id
      WHEN MATCHED THEN
        UPDATE SET 
          email = @email,
          name = @name,
          full_name = @full_name,
          avatar_url = @avatar_url,
          user_id = @user_id,
          token_identifier = @token_identifier,
          updated_at = @updated_at
      WHEN NOT MATCHED THEN
        INSERT (id, email, name, full_name, avatar_url, user_id, token_identifier, created_at, updated_at)
        VALUES (@id, @email, @name, @full_name, @avatar_url, @user_id, @token_identifier, @created_at, @updated_at)
      OUTPUT INSERTED.*;
      `,
      {
        ...user,
        created_at: now,
        updated_at: now,
      }
    );
    return result.recordset[0];
  } catch (error) {
    console.error('Error upserting user:', error);
    throw error;
  }
}

/**
 * Delete user by ID
 */
export async function deleteUser(id: string): Promise<boolean> {
  try {
    const result = await executeQuery(
      'DELETE FROM users WHERE id = @id',
      { id }
    );
    return result.rowsAffected[0] > 0;
  } catch (error) {
    console.error('Error deleting user:', error);
    throw error;
  }
}

// Message-related database operations
export interface StoredMessage {
  id?: number;
  sessionId: string;
  role: 'user' | 'assistant';
  content: string;
  canvasData?: any;
  timestamp: Date;
}

/**
 * Save a message to the database
 */
export async function saveMessage(message: Omit<StoredMessage, 'id' | 'timestamp'>): Promise<StoredMessage> {
  try {
    const result = await executeQuery<StoredMessage>(
      `
      INSERT INTO messages (session_id, role, content, canvas_data, timestamp)
      OUTPUT INSERTED.*
      VALUES (@sessionId, @role, @content, @canvasData, GETUTCDATE())
      `,
      {
        sessionId: message.sessionId,
        role: message.role,
        content: message.content,
        canvasData: message.canvasData ? JSON.stringify(message.canvasData) : null,
      }
    );
    const saved = result.recordset[0];
    return {
      ...saved,
      canvasData: saved.canvas_data ? JSON.parse(saved.canvas_data) : undefined,
    };
  } catch (error) {
    console.error('Error saving message:', error);
    throw error;
  }
}

/**
 * Get messages for a session
 */
export async function getMessagesBySession(sessionId: string): Promise<StoredMessage[]> {
  try {
    const result = await executeQuery<any>(
      'SELECT * FROM messages WHERE session_id = @sessionId ORDER BY timestamp ASC',
      { sessionId }
    );
    return result.recordset.map(row => ({
      id: row.id,
      sessionId: row.session_id,
      role: row.role,
      content: row.content,
      canvasData: row.canvas_data ? JSON.parse(row.canvas_data) : undefined,
      timestamp: row.timestamp,
    }));
  } catch (error) {
    console.error('Error getting messages by session:', error);
    throw error;
  }
}

// User Profile operations
export interface UserProfile {
  id?: string;
  userId: string;
  familySize: number;
  allergies?: string[];
  preferences?: string[];
  createdAt?: Date;
  updatedAt?: Date;
}

/**
 * Get user profile
 */
export async function getUserProfile(userId: string): Promise<UserProfile | null> {
  try {
    const result = await executeQuery<any>(
      'SELECT * FROM user_profiles WHERE user_id = @userId',
      { userId }
    );
    const row = result.recordset[0];
    if (!row) return null;

    return {
      id: row.id,
      userId: row.user_id,
      familySize: row.family_size,
      allergies: row.allergies ? JSON.parse(row.allergies) : [],
      preferences: row.preferences ? JSON.parse(row.preferences) : [],
      createdAt: row.created_at,
      updatedAt: row.updated_at,
    };
  } catch (error) {
    console.error('Error getting user profile:', error);
    throw error;
  }
}

/**
 * Save or update user profile
 */
export async function saveUserProfile(profile: Omit<UserProfile, 'id' | 'createdAt' | 'updatedAt'>): Promise<UserProfile> {
  try {
    const result = await executeQuery<any>(
      `
      MERGE user_profiles AS target
      USING (SELECT @userId as user_id) AS source
      ON target.user_id = source.user_id
      WHEN MATCHED THEN
        UPDATE SET
          family_size = @familySize,
          allergies = @allergies,
          preferences = @preferences,
          updated_at = GETUTCDATE()
      WHEN NOT MATCHED THEN
        INSERT (user_id, family_size, allergies, preferences, created_at, updated_at)
        VALUES (@userId, @familySize, @allergies, @preferences, GETUTCDATE(), GETUTCDATE())
      OUTPUT INSERTED.*;
      `,
      {
        userId: profile.userId,
        familySize: profile.familySize,
        allergies: profile.allergies ? JSON.stringify(profile.allergies) : null,
        preferences: profile.preferences ? JSON.stringify(profile.preferences) : null,
      }
    );

    const saved = result.recordset[0];
    return {
      id: saved.id,
      userId: saved.user_id,
      familySize: saved.family_size,
      allergies: saved.allergies ? JSON.parse(saved.allergies) : [],
      preferences: saved.preferences ? JSON.parse(saved.preferences) : [],
      createdAt: saved.created_at,
      updatedAt: saved.updated_at,
    };
  } catch (error) {
    console.error('Error saving user profile:', error);
    throw error;
  }
}
