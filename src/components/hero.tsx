import Link from "next/link";
import EmailSignupForm from "./EmailSignupForm";

export default function Hero() {
  return (
    <div className="relative overflow-hidden">
      <div className="absolute inset-0 bg-background" />
      <div className="relative pt-16 pb-24 sm:pt-32 sm:pb-40">
        <div className="container mx-auto px-4">
          <div className="text-center max-w-4xl mx-auto">
            <h1 className="font-nunito text-4xl sm:text-6xl font-black text-text mb-8">
              Vardagsmaten löst – med en{" "}
              <span className="text-transparent bg-clip-text bg-gradient-to-r from-primary to-secondary">
                kompis
              </span>{" "}
              vid din sida
            </h1>

            {/* Chat bubbles */}
            <div className="flex flex-col items-center space-y-4 mb-12">
              <div className="bg-neutral-surface p-4 rounded-2xl max-w-xs">
                <p className="text-text text-left">
                  Funkar allt bra med vardagsmaten?
                </p>
              </div>

              <div className="bg-neutral-surface p-4 rounded-2xl max-w-xs">
                <p className="text-text text-left">
                  Vi har en idé som kan göra det enklare att äta billigt, nyttigt och klimatsmart.
                </p>
              </div>

              <div className="bg-neutral-surface p-4 rounded-2xl max-w-xs">
                <p className="text-text text-left">
                  Vill du hjälpa oss att forma den?
                </p>
              </div>
            </div>

            {/* Heading 
            <h2 className="text-xl sm:text-2xl font-bold mb-4">
              Hjälp oss genom att svara på några frågor
            </h2>
            */}

            {/* Button */}
            <Link
              href="https://matvanor.lovable.app/"
              className="inline-block bg-button hover:bg-button-hover text-button-foreground py-4 px-12 rounded-full text-lg transition"
            >
              Ja, jag vill hjälpa till
            </Link>

          </div>
        </div>
      </div>
    </div>
  );
}
