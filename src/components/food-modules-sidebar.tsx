import React from "react";
import { DraggableMatModule } from "./draggable-mat-module";
import { MatModuleProps } from "./MatModule";

// Sample food modules data
export const SAMPLE_FOOD_MODULES: MatModuleProps[] = [
  {
    id: "tomat<PERSON><PERSON>",
    name: "<PERSON><PERSON><PERSON><PERSON>",
    matchingFamilyPercent: 90,
  },
  {
    id: "rostad-potatis",
    name: "Rostad potatis",
    matchingFamilyPercent: 95,
  },
  {
    id: "kikartsbiffar",
    name: "<PERSON><PERSON><PERSON><PERSON>bi<PERSON><PERSON>",
    matchingFamilyPercent: 70,
  },
  {
    id: "fetaostkram",
    name: "<PERSON><PERSON>ostkr<PERSON><PERSON>",
    matchingFamilyPercent: 65,
  },
  {
    id: "stekta-gronsaker",
    name: "<PERSON><PERSON><PERSON>r<PERSON>",
    matchingFamilyPercent: 80,
  },
  {
    id: "marinerad-tofu",
    name: "<PERSON>rad tofu",
    matchingFamilyPercent: 60,
  },
  {
    id: "kryddiga-bonor",
    name: "<PERSON><PERSON><PERSON><PERSON> bönor",
    matchingFamilyPercent: 75,
  },
  {
    id: "orts<PERSON>",
    name: "<PERSON><PERSON><PERSON><PERSON>",
    matchingFamilyPercent: 85,
  },
  {
    id: "ugnsbakad-lax",
    name: "Ugnsbakad lax",
    matchingFamilyPercent: 80,
  },
];

export function FoodModulesSidebar() {
  return (
    <div className="bg-white rounded-xl p-6 border shadow-sm">
      <h2 className="font-semibold text-xl mb-4">Matmoduler</h2>
      <div className="relative mb-4">
        <input
          type="text"
          placeholder="Sök mat..."
          className="w-full p-2 pl-8 border rounded-md text-sm"
        />
        <svg
          xmlns="http://www.w3.org/2000/svg"
          className="h-4 w-4 absolute left-2 top-3 text-gray-400"
          fill="none"
          viewBox="0 0 24 24"
          stroke="currentColor"
        >
          <path
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth={2}
            d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"
          />
        </svg>
      </div>

      <div className="flex gap-2 mb-4 overflow-x-auto">
        <button className="px-3 py-1 text-xs bg-matkompis-green-500 text-white rounded-full hover:bg-matkompis-green-700">
          Alla
        </button>
        <button className="px-3 py-1 text-xs border rounded-full hover:bg-gray-100">
          Favoriter
        </button>
        <button className="px-3 py-1 text-xs border rounded-full hover:bg-gray-100">
          Filtrera
        </button>
      </div>

      <div className="space-y-2 overflow-y-auto pr-1">
        {SAMPLE_FOOD_MODULES.map((module) => (
          <DraggableMatModule key={module.id} module={module} />
        ))}
      </div>
    </div>
  );
} 