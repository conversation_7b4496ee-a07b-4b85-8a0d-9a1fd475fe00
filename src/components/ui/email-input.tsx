'use client';

import React from "react";
import { cn } from "@/lib/utils";
import { isValidEmail } from "@/utils/validation";

interface EmailInputProps extends React.InputHTMLAttributes<HTMLInputElement> {
  onValidationChange?: (isValid: boolean) => void;
  error?: string;
  showValidation?: boolean;
}

export function EmailInput({
  className,
  onValidationChange,
  error,
  value,
  showValidation = false,
  ...props
}: EmailInputProps) {
  const [isValid, setIsValid] = React.useState(true);

  React.useEffect(() => {
    if (typeof value === "string") {
      const valid = value === "" || isValidEmail(value);
      setIsValid(valid);
      onValidationChange?.(valid);
    }
  }, [value, onValidationChange]);

  return (
    <div className="flex flex-col gap-1">
      <input
        type="text"
        className={cn(
          "flex h-10 w-full rounded-lg border border-gray-300 bg-white px-3 py-2 ring-offset-white placeholder:text-gray-500 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-matkompis-primary focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",
          showValidation && !isValid && "border-warning focus-visible:ring-warning",
          className
        )}
        value={value}
        {...props}
      />
      {showValidation && error && (
        <p className="text-sm text-warning text-left">{error}</p>
      )}
    </div>
  );
} 