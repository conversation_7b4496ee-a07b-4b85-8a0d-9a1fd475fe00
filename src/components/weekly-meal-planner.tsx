import React from "react";
import { cn } from "@/lib/utils";
import { MatModuleProps } from "./MatModule";
import { DroppableMealSlot } from "./droppable-meal-slot";

// Days of the week in Swedish
export const DAYS = [
  "måndag",
  "tisdag",
  "onsdag",
  "torsdag",
  "fredag",
  "lördag",
  "söndag",
];

// Meal types
export const MEAL_TYPES = ["Middag", "Lunch", "Frukost", "<PERSON>lis", "Alla måltider"];

export function WeeklyMealPlanner({
  mealPlan,
  onDrop,
  selectedMealType,
  setSelectedMealType,
}: {
  mealPlan: Record<string, MatModuleProps[]>;
  onDrop: (item: MatModuleProps, day: string) => void;
  selectedMealType: string;
  setSelectedMealType: (type: string) => void;
}) {
  return (
    <div className="md:col-span-3 bg-white rounded-xl p-6 border shadow-sm">
      {/* Meal type filters */}
      <div className="flex gap-2 mb-6 overflow-x-auto pb-2">
        {MEAL_TYPES.map((type) => (
          <button
            key={type}
            onClick={() => setSelectedMealType(type)}
            className={cn(
              "px-4 py-2 text-sm rounded-md whitespace-nowrap transition-colors",
              type === selectedMealType
                ? "bg-blue-500 text-white"
                : "bg-gray-100 hover:bg-gray-200"
            )}
          >
            {type}
          </button>
        ))}
      </div>

      {/* Days grid */}
      <div className="grid grid-cols-2 gap-4">
        {DAYS.map((day) => (
          <div key={day}>
            <h3 className="font-medium mb-2 capitalize">{day}</h3>
            <DroppableMealSlot
              day={day}
              selectedUser="A" // TODO: change to user id
              onDrop={onDrop}
              modules={mealPlan[day] || []}
            />
          </div>
        ))}
      </div>
    </div>
  );
} 