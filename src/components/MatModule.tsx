import { CircleUser } from "lucide-react";
import { cn } from "@/lib/utils";

export interface MatModuleProps {
  id: string;
  name: string;
  matchingFamilyPercent?: number;
  isDragging?: boolean;
  isDropped?: boolean;
  onClick?: () => void;
}

export default function MatModule({
  id,
  name,
  matchingFamilyPercent,
  isDragging = false,
  isDropped = false,
  onClick,
}: MatModuleProps) {
  return (
    <div
      className={cn(
        "cursor-move transition-all",
        isDropped ? "p-2 text-sm" : "p-3 bg-white border rounded-md shadow-sm hover:shadow-md",
        isDragging ? "opacity-50 shadow-md" : "opacity-100"
      )}
      onClick={onClick}
      data-module-id={id}
    >
      {isDropped ? (
        <div className="flex items-center justify-between">
          <span>{name}</span>
          {matchingFamilyPercent !== undefined && (
            <span className="text-xs text-green-600 ml-2">{matchingFamilyPercent}%</span>
          )}
        </div>
      ) : (
        <div className="flex items-center justify-between">
          <h3 className="font-medium text-sm">{name}</h3>
          <div className="flex items-center">
            <CircleUser size={16} className="text-blue-500 mr-1" />
            {matchingFamilyPercent !== undefined && (
              <span className="text-xs text-green-600">{matchingFamilyPercent}%</span>
            )}
          </div>
        </div>
      )}
    </div>
  );
}
