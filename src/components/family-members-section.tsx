import React from "react";

export function FamilyMembersSection() {
  return (
    <section className="bg-white rounded-xl p-6 border shadow-sm">
      <h2 className="font-semibold text-xl mb-4">Familjemedlemmar</h2>
      <div className="flex gap-4 overflow-x-auto pb-2">
        {/* Sample family members */}
        <div className="flex flex-col items-center">
          <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mb-2">
            <span className="text-2xl">👩</span>
          </div>
          <span className="text-sm font-medium">Anna</span>
          <span className="text-xs text-green-600">Aktiv</span>
        </div>
        <div className="flex flex-col items-center">
          <div className="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mb-2">
            <span className="text-2xl">👨</span>
          </div>
          <span className="text-sm font-medium">Erik</span>
        </div>
        <div className="flex flex-col items-center">
          <div className="w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mb-2">
            <span className="text-2xl">👧</span>
          </div>
          <span className="text-sm font-medium">Maja</span>
        </div>
        <div className="flex flex-col items-center">
          <div className="w-16 h-16 bg-purple-100 rounded-full flex items-center justify-center mb-2">
            <span className="text-2xl">👦</span>
          </div>
          <span className="text-sm font-medium">Liam</span>
        </div>
        <div className="flex flex-col items-center">
          <div className="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mb-2">
            <span className="text-2xl">⚙️</span>
          </div>
          <span className="text-sm font-medium">Inställningar</span>
        </div>
      </div>
    </section>
  );
} 