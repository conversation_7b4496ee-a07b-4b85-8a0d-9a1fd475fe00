import { cn } from "@/lib/utils"

const benefits = [
  {
    title: "Personlig Matplanering",
    description: "Skräddarsydda matplaner baserade på dina preferenser och behov",
    icon: (
      <svg 
        className="w-8 h-8" 
        viewBox="0 0 24 24" 
        style={{ color: '#2D9CDB' }}
        xmlns="http://www.w3.org/2000/svg"
      >
        <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm5 11h-4v4h-2v-4H7v-2h4V7h2v4h4v2z"/>
      </svg>
    ),
  },
  {
    title: "Smart Inköpslista",
    description: "Automatiskt genererade inköpslistor som sparar tid och pengar",
    icon: (
      <svg 
        className="w-8 h-8" 
        viewBox="0 0 24 24" 
        style={{ color: '#641E78' }}
        xmlns="http://www.w3.org/2000/svg"
      >
        <path d="M19 3H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zm0 16H5V5h14v14z"/>
        <path d="M7 7h10v2H7zm0 4h10v2H7zm0 4h7v2H7z"/>
      </svg>
    ),
  },
  {
    title: "Hälsosamma Recept",
    description: "Nyttiga och läckra recept som passar din livsstil",
    icon: (
      <svg 
        className="w-8 h-8" 
        viewBox="0 0 24 24" 
        style={{ color: '#157F3E' }}
        xmlns="http://www.w3.org/2000/svg"
      >
        <path d="M8.1 13.34l2.83-2.83L3.91 3.5c-1.56 1.56-1.56 4.09 0 5.66l4.19 4.18zm6.78-1.81c1.53.71 3.68.21 5.27-1.38 1.91-1.91 2.28-4.65.81-6.12-1.46-1.46-4.2-1.1-6.12.81-1.59 1.59-2.09 3.74-1.38 5.27L3.7 19.87l1.41 1.41L12 14.41l6.88 6.88 1.41-1.41L13.41 13l1.47-1.47z"/>
      </svg>
    ),
  },
  {
    title: "Kostnadsoptimering",
    description: "Spara pengar genom smart matplanering och inköp",
    icon: (
      <svg 
        className="w-8 h-8" 
        viewBox="0 0 24 24" 
        style={{ color: '#F26430' }}
        xmlns="http://www.w3.org/2000/svg"
      >
        <path d="M11.8 10.9c-2.27-.59-3-1.2-3-2.15 0-1.09 1.01-1.85 2.7-1.85 1.78 0 2.44.85 2.5 2.1h2.21c-.07-1.72-1.12-3.3-3.21-3.81V3h-3v2.16c-1.94.42-3.5 1.68-3.5 3.61 0 2.31 1.91 3.46 4.7 4.13 2.5.6 3 1.48 3 2.41 0 .69-.49 1.79-2.7 1.79-2.06 0-2.87-.92-2.98-2.1h-2.2c.12 2.19 1.76 3.42 3.68 3.83V21h3v-2.15c1.95-.37 3.5-1.5 3.5-3.55 0-2.84-2.43-3.81-4.7-4.4z"/>
      </svg>
    ),
  },
]

export function BenefitCards() {
  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
      {benefits.map((benefit) => (
        <div
          key={benefit.title}
          className="p-6 rounded-xl bg-white shadow-sm hover:shadow-md transition-shadow"
        >
          <div className="mb-4">
            {benefit.icon}
          </div>
          <h3 className="text-xl font-semibold mb-2 text-matkompis-logo-text">{benefit.title}</h3>
          <p className="text-gray-600">{benefit.description}</p>
        </div>
      ))}
    </div>
  )
} 