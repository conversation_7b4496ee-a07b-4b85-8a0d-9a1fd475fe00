import React from "react";
import { useDrag } from "react-dnd";
import MatModule, { MatModuleProps } from "./MatModule";

// Types for drag and drop
export type ItemType = "food-module";

export function DraggableMatModule({ module }: { module: MatModuleProps }) {
  const [{ isDragging }, drag] = useDrag<MatModuleProps, void, { isDragging: boolean }>(() => ({
    type: "food-module" as ItemType,
    item: { ...module },
    collect: (monitor) => ({
      isDragging: !!monitor.isDragging(),
    }),
  }));

  return (
    <div ref={drag as any} className="mb-2">
      <MatModule {...module} isDragging={isDragging} />
    </div>
  );
} 