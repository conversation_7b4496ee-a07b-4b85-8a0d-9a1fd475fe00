"use client";

import { useState, useEffect } from "react";
import { DndProvider } from "react-dnd";
import { HTML5Backend } from "react-dnd-html5-backend";
import { MatModuleProps } from "./MatModule";
import { FamilyMembersSection } from "./family-members-section";
import { FoodModulesSidebar } from "./food-modules-sidebar";
import { WeeklyMealPlanner, DAYS } from "./weekly-meal-planner";
import { NudgingSection } from "./nudging-section";

export default function MealPlannerDashboard() {
  // State for meal plan
  const [mealPlan, setMealPlan] = useState<Record<string, MatModuleProps[]>>({});
  const [selectedMealType, setSelectedMealType] = useState("Middag");

  // Initialize meal plan
  useEffect(() => {
    const initialMealPlan: Record<string, MatModuleProps[]> = {};
    DAYS.forEach((day) => {
      initialMealPlan[day] = [];
    });
    setMealPlan(initialMealPlan);
  }, []);

  // Handle dropping a food module into a meal slot
  const handleDrop = (item: MatModuleProps, day: string) => {
    setMealPlan((prev) => {
      const currentModules = prev[day] || [];
      // Check if the item is already in the day's modules
      if (currentModules.some((module) => module.id === item.id)) {
        return prev;
      }
      return {
        ...prev,
        [day]: [...currentModules, { 
          id: item.id, 
          name: item.name,
          matchingFamilyPercent: item.matchingFamilyPercent 
        }],
      };
    });
  };

  return (
    <DndProvider backend={HTML5Backend}>
      <div className="grid grid-cols-1 gap-6">
        {/* Family members section */}
        <FamilyMembersSection />

        {/* Main meal planner section */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
          {/* Food modules sidebar */}
          <FoodModulesSidebar />

          {/* Weekly meal planner */}
          <WeeklyMealPlanner 
            mealPlan={mealPlan}
            onDrop={handleDrop}
            selectedMealType={selectedMealType}
            setSelectedMealType={setSelectedMealType}
          />
        </div>

        {/* Nudging section */}
        <NudgingSection />
      </div>
    </DndProvider>
  );
}
