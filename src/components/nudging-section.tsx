import React from "react";
import { DollarSign, Heart, Leaf } from "lucide-react";

export function NudgingSection() {
  return (
    <section className="bg-white rounded-xl p-6 border shadow-sm">
      <h2 className="font-semibold text-xl mb-4">Nudging</h2>
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <div className="space-y-2">
          <div className="flex justify-between items-center">
            <div className="flex items-center">
              <DollarSign size={16} className="text-amber-500 mr-1" />
              <span className="font-medium">Ekonomi</span>
            </div>
            <span className="text-sm">65%</span>
          </div>
          <div className="w-full bg-gray-200 rounded-full h-2">
            <div
              className="bg-amber-500 h-2 rounded-full"
              style={{ width: "65%" }}
            ></div>
          </div>
          <p className="text-xs text-gray-600">
            Byt ut nötkött mot kyckling för att spara pengar denna vecka.
          </p>
        </div>

        <div className="space-y-2">
          <div className="flex justify-between items-center">
            <div className="flex items-center">
              <Heart size={16} className="text-rose-500 mr-1" />
              <span className="font-medium">Hälsa</span>
            </div>
            <span className="text-sm">80%</span>
          </div>
          <div className="w-full bg-gray-200 rounded-full h-2">
            <div
              className="bg-rose-500 h-2 rounded-full"
              style={{ width: "80%" }}
            ></div>
          </div>
          <p className="text-xs text-gray-600">
            Bra balans av näringsämnen! Lägg till mer grönsaker för ännu
            bättre hälsa.
          </p>
        </div>

        <div className="space-y-2">
          <div className="flex justify-between items-center">
            <div className="flex items-center">
              <Leaf size={16} className="text-green-500 mr-1" />
              <span className="font-medium">Klimat</span>
            </div>
            <span className="text-sm">45%</span>
          </div>
          <div className="w-full bg-gray-200 rounded-full h-2">
            <div
              className="bg-matkompis-green-500 h-2 rounded-full"
              style={{ width: "45%" }}
            ></div>
          </div>
          <p className="text-xs text-gray-600">
            Prova att ha en vegetarisk dag i veckan för att minska
            klimatpåverkan.
          </p>
        </div>

        <div className="space-y-2">
          <div className="flex justify-between items-center">
            <div className="flex items-center">
              <svg
                xmlns="http://www.w3.org/2000/svg"
                className="h-4 w-4 text-indigo-500 mr-1"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z"
                />
              </svg>
              <span className="font-medium">Biologisk mångfald</span>
            </div>
            <span className="text-sm">30%</span>
          </div>
          <div className="w-full bg-gray-200 rounded-full h-2">
            <div
              className="bg-indigo-500 h-2 rounded-full"
              style={{ width: "30%" }}
            ></div>
          </div>
          <p className="text-xs text-gray-600">
            Välj MSC-märkt fisk och lokala grönsaker för att stödja
            biologisk mångfald.
          </p>
        </div>
      </div>
    </section>
  );
} 