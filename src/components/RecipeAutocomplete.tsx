"use client";

import React, { useState, useEffect, useRef } from "react";
import Fuse from "fuse.js";
import { Recipe } from '@/types/recipe';
import { cn } from "@/lib/utils";

// Component props
interface RecipeAutocompleteProps {
  onSelect: (recipe: Recipe) => void;
  selectedUser: string;
  className?: string;
}

export function RecipeAutocomplete({ onSelect, selectedUser, className }: RecipeAutocompleteProps) {
  const [recipes, setRecipes] = useState<Recipe[]>([]);
  const [searchTerm, setSearchTerm] = useState("");
  const [suggestions, setSuggestions] = useState<Recipe[]>([]);
  const [showSuggestions, setShowSuggestions] = useState(false);
  const inputRef = useRef<HTMLInputElement>(null);
  const dropdownRef = useRef<HTMLDivElement>(null);

  // Load recipes from JSON file
  useEffect(() => {
    fetch('/data/recipes.json')
      .then(res => res.json())
      .then(data => setRecipes(data))
      .catch(err => console.error('Error loading recipes:', err));
  }, []);

  useEffect(() => {
    const fuse = new Fuse(recipes, {
      keys: ['name', 'synonyms'],
      threshold: 0.4,
    });

    if (searchTerm) {
      let results = fuse.search(searchTerm).map(result => result.item);
      // Sort by user preference
      results.sort((a, b) => (b.preferences[selectedUser] || 0) - (a.preferences[selectedUser] || 0));
      setSuggestions(results);
    } else {
      setSuggestions([]);
    }
  }, [searchTerm, recipes, selectedUser]);

  useEffect(() => {
    function handleClickOutside(event: MouseEvent) {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setShowSuggestions(false);
      }
    }
    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setSearchTerm(e.target.value);
    setShowSuggestions(true);
  };

  const handleSuggestionClick = (recipe: Recipe) => {
    onSelect(recipe);
    setSearchTerm('');
    setShowSuggestions(false);
  };

  return (
    <div className={cn("relative w-full", className)} ref={dropdownRef}>
      <div className="relative">
        <input
          ref={inputRef}
          type="text"
          value={searchTerm}
          onChange={handleInputChange}
          onFocus={() => setShowSuggestions(true)}
          placeholder="Jag vill äta..."
          className="w-full pl-10 pr-4 py-2 text-gray-400 bg-white rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 transition-all"
        />
        <div className="absolute left-3 top-1/2 transform -translate-y-1/2">
          <svg className="w-5 h-5 text-gray-400" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M21 21L16.65 16.65M19 11C19 15.4183 15.4183 19 11 19C6.58172 19 3 15.4183 3 11C3 6.58172 6.58172 3 11 3C15.4183 3 19 6.58172 19 11Z" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
          </svg>
        </div>
      </div>
      
      {showSuggestions && suggestions.length > 0 && (
        <div className="absolute z-10 w-full mt-1 bg-white rounded-lg shadow-lg border border-gray-200">
          {suggestions.map((recipe, index) => (
            <div
              key={recipe.name}
              onClick={() => handleSuggestionClick(recipe)}
              className={`px-4 py-2 cursor-pointer hover:bg-gray-50 ${
                index !== suggestions.length - 1 ? 'border-b border-gray-100' : ''
              }`}
            >
              <div className="font-medium">{recipe.name}</div>
              <div className="text-sm text-gray-500">
                {Math.round(recipe.preferences[selectedUser])}% matchning
              </div>
            </div>
          ))}
        </div>
      )}
    </div>
  );
} 