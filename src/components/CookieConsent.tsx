'use client';

import { useState, useEffect } from 'react';
import { X } from 'lucide-react';

declare global {
  interface Window {
    'ga-disable-G-2JGWZS7N4D'?: boolean;
  }
}

export default function CookieConsent() {
  const [showBanner, setShowBanner] = useState(false);

  useEffect(() => {
    const consent = localStorage.getItem('cookieConsent');
    if (!consent) {
      setShowBanner(true);
    }
  }, []);

  const handleAccept = () => {
    localStorage.setItem('cookieConsent', 'true');
    setShowBanner(false);
  };

  const handleReject = () => {
    localStorage.setItem('cookieConsent', 'false');
    setShowBanner(false);
    // Remove Google Analytics if it exists
    if (typeof window !== 'undefined') {
      window['ga-disable-G-2JGWZS7N4D'] = true;
    }
  };

  if (!showBanner) return null;

  return (
    <>
      {/* Backdrop */}
      <div className="fixed inset-0 bg-black bg-opacity-50 z-50" />
      
      {/* Modal */}
      <div className="fixed inset-0 flex items-center justify-center z-50 p-4">
        <div className="bg-card rounded-lg shadow-xl max-w-lg w-full mx-auto">
          <div className="p-6">
            <div className="flex justify-between items-start mb-4">
              <h2 className="text-xl font-semibold text-text">Cookie-inställningar</h2>
              <button
                onClick={() => setShowBanner(false)}
                className="text-surface"
              >
                <X className="h-5 w-5 text-text" />
              </button>
            </div>
            <p className="text-muted-foreground mb-6">
              Vi använder cookies för att förbättra din upplevelse och för att analysera trafik. 
              Genom att klicka på "Acceptera" godkänner du vår användning av cookies.
            </p>
            <div className="flex flex-col sm:flex-row gap-3 justify-end">
              <button
                onClick={handleReject}
                className="px-4 py-2 text-text text-muted-foreground"
              >
                Avvisa
              </button>
              <button
                onClick={handleAccept}
                className="px-4 py-2 bg-primary text-button rounded-lg hover:bg-accent transition-colors"
              >
                Acceptera
              </button>
            </div>
          </div>
        </div>
      </div>
    </>
  );
} 