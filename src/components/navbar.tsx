import Link from "next/link";
import { createClient } from "../../supabase/server";
import { Button } from "./ui/button";
import { User, UserCircle, Utensils } from "lucide-react";
import UserProfile from "./user-profile";
import { Logo } from './Logo';

export default async function Navbar() {
  const supabase = createClient();

  const {
    data: { user },
  } = await (await supabase).auth.getUser();

  return (
    <nav className="w-full border-b border-border bg-backgroundAlt py-2">
      <div className="container mx-auto px-4 flex justify-between items-center">
        <Link href="/" prefetch className="text-xl font-bold flex items-center">
          <Logo />
        </Link>
        <div className="flex gap-4 items-center">
          {/* TODO: Add login and dashboard links
          {user ? (
            <>
              <Link
                href="/dashboard"
                className="px-4 py-2 text-sm font-medium text-gray-700 hover:text-gray-900"
              >
                <Button className="bg-matkompis-green-500 hover:bg-matkompis-green-700">
                  Dashboard
                </Button>
              </Link>
              <UserProfile />
            </>
          ) : (
            <>
              <Link
                href="/sign-in"
                className="px-4 py-2 text-sm font-medium text-gray-700 hover:text-gray-900"
              >
                Logga in
              </Link>
              <Link
                href="/sign-up"
                className="px-4 py-2 text-sm font-medium text-white bg-primary rounded-md hover:bg-accent"
              >
                Skapa konto
              </Link>
            </>
          )}
            */}
        </div>
      </div>
    </nav>
  );
}
