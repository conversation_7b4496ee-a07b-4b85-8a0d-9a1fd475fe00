import React, { useState, useRef } from "react";
import { useDrop } from "react-dnd";
import { cn } from "@/lib/utils";
import MatModule, { MatModuleProps } from "./MatModule";
import { ItemType } from "./draggable-mat-module";
import { RecipeAutocomplete } from "./RecipeAutocomplete";
import { Recipe } from "../types/recipe";

interface DroppableMealSlotProps {
  onDrop: (item: MatModuleProps, day: string) => void;
  day: string;
  selectedUser: string;
  modules: MatModuleProps[];
}

export function DroppableMealSlot({ onDrop, day, selectedUser, modules }: DroppableMealSlotProps) {
  const [{ isOver }, drop] = useDrop(() => ({
    accept: "food-module" as ItemType,
    drop: (item: MatModuleProps) => onDrop(item, day),
    collect: (monitor) => ({
      isOver: monitor.isOver(),
    }),
  }));

  const handleRecipeSelect = (recipe: Recipe) => {
    const newModule: MatModuleProps = {
      id: Math.random().toString(36).substr(2, 9),
      name: recipe.name,
      matchingFamilyPercent: recipe.preferences[selectedUser] || 0,
      isDragging: false,
    };
    onDrop(newModule, day);
  };

  return (
    <div
      ref={drop as any}
      className={`p-4 rounded-lg border-2 bg-shadow-green-50 border-dashed transition-colors min-h-[200px] ${
        isOver ? "border-blue-500 bg-blue-50" : "border-gray-300"
      }`}
    >
      <div className="space-y-2">
        {modules.map((module) => (
          <MatModule key={module.id} {...module} isDropped={true} />
        ))}
        <RecipeAutocomplete
          onSelect={handleRecipeSelect}
          selectedUser={selectedUser}
        />
      </div>
    </div>
  );
} 