import React from 'react';

export default function Canvas() {
  return (
    <div className="h-full w-full bg-background-alt flex flex-col">
      {/* <PERSON>vas Header */}
      <div className="p-4 border-b border-border hidden md:block">
        <h2 className="text-lg font-semibold text-text">Canvas</h2>
      </div>
      {/* Canvas Content Area */}
      <div className="flex-1 p-4 overflow-y-auto">
        <div className="h-full flex items-center justify-center">
          {/* Container with ribs showing background image */}
          <div
            className="relative w-full max-w-[800px] overflow-hidden bg-background-alt" 
            style={{
              aspectRatio: '3000 / 2980', // Same as original SVG
              border: '2px solid purple',
            }}
          >
            {/* Individual rounded ribs - each showing the correct portion of one large image */}
            <div className="absolute inset-0">
              {/* Row 1: Full width (y=0-400) */}
              <div 
                className="absolute rounded-lg"
                style={{
                  top: '0%',
                  left: '0%',
                  width: '100%',
                  height: '13.42%',
                  borderRadius: '3.33%',
                  backgroundImage: "url('/images/mealplan.jpg')",
                  backgroundSize: '100% 745%', // Scale to container size (100% width, 745% height to cover all ribs)
                  backgroundRepeat: 'no-repeat',
                  backgroundPosition: '0% 0%', // Top portion of image
                }}
              />
              
              {/* Row 2: Full width (y=430-830) */}
              <div 
                className="absolute rounded-lg"
                style={{
                  top: '14.43%',
                  left: '0%',
                  width: '100%',
                  height: '13.42%',
                  borderRadius: '3.33%',
                  backgroundImage: "url('/images/mealplan.jpg')",
                  backgroundSize: '100% 745%',
                  backgroundRepeat: 'no-repeat',
                  backgroundPosition: '0% -107.5%', // Offset to show second portion
                }}
              />
              
              {/* Row 3: Left segment (y=860-1260, w=2057.5) */}
              <div 
                className="absolute rounded-lg"
                style={{
                  top: '28.86%',
                  left: '0%',
                  width: '68.58%',
                  height: '13.42%',
                  borderRadius: '3.33%',
                  backgroundImage: "url('/images/mealplan.jpg')",
                  backgroundSize: '145.8% 745%', // Wider to account for partial width
                  backgroundRepeat: 'no-repeat',
                  backgroundPosition: '0% -215%', // Offset to show third portion
                }}
              />
              
              {/* Row 3: Right segment (x=2087.5, y=860-1260, w=912.497) */}
              <div 
                className="absolute rounded-lg"
                style={{
                  top: '28.86%',
                  left: '69.58%',
                  width: '30.42%',
                  height: '13.42%',
                  borderRadius: '3.33%',
                  backgroundImage: "url('/images/mealplan.jpg')",
                  backgroundSize: '328.7% 745%', // Much wider for small segment
                  backgroundRepeat: 'no-repeat',
                  backgroundPosition: '-228.7% -215%', // Offset for right portion
                }}
              />
              
              {/* Row 4: Left segment (y=1290-1690, w=1270.17) */}
              <div 
                className="absolute rounded-lg"
                style={{
                  top: '43.29%',
                  left: '0%',
                  width: '42.34%',
                  height: '13.42%',
                  borderRadius: '3.33%',
                  backgroundImage: "url('/images/mealplan.jpg')",
                  backgroundSize: '236.2% 745%',
                  backgroundRepeat: 'no-repeat',
                  backgroundPosition: '0% -322.5%',
                }}
              />
              
              {/* Row 4: Middle segment (x=1300.17, y=1290-1690, w=757.333) */}
              <div 
                className="absolute rounded-lg"
                style={{
                  top: '43.29%',
                  left: '43.34%',
                  width: '25.24%',
                  height: '13.42%',
                  borderRadius: '3.33%',
                  backgroundImage: "url('/images/mealplan.jpg')",
                  backgroundSize: '396.2% 745%',
                  backgroundRepeat: 'no-repeat',
                  backgroundPosition: '-171.7% -322.5%',
                }}
              />
              
              {/* Row 4: Right segment (x=2087.5, y=1290-1690, w=912.497) */}
              <div 
                className="absolute rounded-lg"
                style={{
                  top: '43.29%',
                  left: '69.58%',
                  width: '30.42%',
                  height: '13.42%',
                  borderRadius: '3.33%',
                  backgroundImage: "url('/images/mealplan.jpg')",
                  backgroundSize: '328.7% 745%',
                  backgroundRepeat: 'no-repeat',
                  backgroundPosition: '-228.7% -322.5%',
                }}
              />
              
              {/* Row 5: Left segment (y=1720-2120, w=912.497) */}
              <div 
                className="absolute rounded-lg"
                style={{
                  top: '57.72%',
                  left: '0%',
                  width: '30.42%',
                  height: '13.42%',
                  borderRadius: '3.33%',
                  backgroundImage: "url('/images/mealplan.jpg')",
                  backgroundSize: '328.7% 745%',
                  backgroundRepeat: 'no-repeat',
                  backgroundPosition: '0% -430%',
                }}
              />
              
              {/* Row 5: Right segment (x=942.497, y=1720-2120, w=2057.5) */}
              <div 
                className="absolute rounded-lg"
                style={{
                  top: '57.72%',
                  left: '31.42%',
                  width: '68.58%',
                  height: '13.42%',
                  borderRadius: '3.33%',
                  backgroundImage: "url('/images/mealplan.jpg')",
                  backgroundSize: '145.8% 745%',
                  backgroundRepeat: 'no-repeat',
                  backgroundPosition: '-45.8% -430%',
                }}
              />
              
              {/* Row 6: Full width (y=2150-2550) */}
              <div 
                className="absolute rounded-lg"
                style={{
                  top: '72.15%',
                  left: '0%',
                  width: '100%',
                  height: '13.42%',
                  borderRadius: '3.33%',
                  backgroundImage: "url('/images/mealplan.jpg')",
                  backgroundSize: '100% 745%',
                  backgroundRepeat: 'no-repeat',
                  backgroundPosition: '0% -537.5%',
                }}
              />
              
              {/* Row 7: Full width (y=2580-2980) */}
              <div 
                className="absolute rounded-lg"
                style={{
                  top: '86.58%',
                  left: '0%',
                  width: '100%',
                  height: '13.42%',
                  borderRadius: '3.33%',
                  backgroundImage: "url('/images/mealplan.jpg')",
                  backgroundSize: '100% 745%',
                  backgroundRepeat: 'no-repeat',
                  backgroundPosition: '0% -645%',
                }}
              />
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
