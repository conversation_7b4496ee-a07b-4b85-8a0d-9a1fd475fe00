"use client";

import { useState } from "react";
import { Check, <PERSON>Handshake, ThumbsUp } from "lucide-react";
import { EmailInput } from "./ui/email-input";
import { createClient } from "../../supabase/client";

declare global {
  interface Window {
    gtag: (...args: any[]) => void;
  }
}

export default function EmailSignupForm() {
  const [email, setEmail] = useState("");
  const [status, setStatus] = useState<"idle" | "loading" | "success" | "error">("idle");
  const [errorMessage, setErrorMessage] = useState("");
  const [isEmailValid, setIsEmailValid] = useState(false);
  const [showValidation, setShowValidation] = useState(false);
  const supabase = createClient();

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setShowValidation(true);
    
    if (!email.trim()) {
      setStatus("error");
      setErrorMessage("Vänligen ange en e-postadress");
      return;
    }
    
    if (!isEmailValid) {
      setStatus("error");
      setErrorMessage("Vänligen kontrollera att e-postadressen är korrekt");
      return;
    }

    setStatus("loading");
    setErrorMessage("");

    try {
      const { error } = await supabase
        .from('early_access_signups')
        .insert([
          { 
            email,
            source: document.referrer || 'direct'
          }
        ]);

      if (error) {
        if (error.code === '23505') { // Unique constraint violation
          setStatus("error");
          setErrorMessage("Denna e-postadress är redan registrerad");
        } else {
          throw new Error(error.message);
        }
        return;
      }

      // Track successful signup in Google Analytics (production only)
      if (process.env.NODE_ENV === 'production' && typeof window !== 'undefined' && window.gtag) {
        window.gtag('event', 'sign_up', {
          method: 'email',
          source: document.referrer || 'direct'
        });
      }

      setStatus("success");
      setEmail("");
    } catch (error) {
      setStatus("error");
      setErrorMessage(error instanceof Error ? error.message : "Ett fel uppstod");
    }
  };

  if (status === "success") {
    return (
      <div className="text-center p-6 bg-climate-surface rounded-lg">
        <div className="flex items-center justify-center gap-2 text-climate mb-2">
          <HeartHandshake className="w-5 h-5" />
          <span className="font-medium">Tack för din registrering!</span>
        </div>
        <p className="text-muted-foreground">
          Vi hör av oss när vi har mer att visa eller om vi behöver fråga dig något.
        </p>
      </div>
    );
  }

  return (
    <form onSubmit={handleSubmit} className="max-w-md mx-auto">
      <div className="flex flex-col sm:flex-row gap-2">
        <div className="flex-1 min-w-0">
          <EmailInput
            value={email}
            onChange={(e) => setEmail(e.target.value)}
            placeholder="Din e-postadress"
            onValidationChange={setIsEmailValid}
            error={status === "error" ? errorMessage : undefined}
            disabled={status === "loading"}
            showValidation={showValidation}
            className="w-full"
          />
        </div>
        <button
          type="submit"
          disabled={status === "loading"}
          className="w-full sm:w-auto px-6 py-2 
          bg-button text-button-foreground hover:bg-button-hover rounded-lg 
          transition-colors disabled:opacity-50 whitespace-nowrap h-10"
        >
          {status === "loading" ? "Skickar..." : "Anmäl intresse"}
        </button>
      </div>
    </form>
  );
} 