'use client';

import React, { useState, useRef, useEffect } from 'react';
import { Send } from 'lucide-react';
import ReactMarkdown from 'react-markdown';
import { Message, ChatResponse } from '@/lib/types';
import { db, StoredMessage } from '@/lib/db';

interface ChatProps {
  onCanvasContent?: (content: any) => void;
}

export default function Chat({ onCanvasContent }: ChatProps) {
  const [messages, setMessages] = useState<Message[]>([]);
  const [input, setInput] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [sessionId, setSessionId] = useState<string | null>(null);
  const messagesEndRef = useRef<HTMLDivElement>(null);

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  // Load session ID and messages from storage on mount
  useEffect(() => {
    const loadStoredData = async () => {
      // Use sessionStorage for session ID (per-tab isolation, no incognito persistence)
      const savedSessionId = sessionStorage.getItem('matkompis_session_id');
      if (savedSessionId) {
        setSessionId(savedSessionId);
        
        // Load conversation history for this session
        try {
          const storedMessages = await db.messages
            .where('sessionId')
            .equals(savedSessionId)
            .toArray();
            
          // Sort by timestamp and convert to Message format
          const sortedMessages = storedMessages.sort((a, b) => a.timestamp.getTime() - b.timestamp.getTime());
          const messages: Message[] = sortedMessages.map((storedMessage: StoredMessage) => ({
            id: storedMessage.id,
            content: storedMessage.content,
            role: storedMessage.role,
            timestamp: storedMessage.timestamp,
            canvas: storedMessage.canvas
          }));
          setMessages(messages);
        } catch (error) {
          console.error('Error loading messages:', error);
        }
      }
    };
    
    loadStoredData();
  }, []);

  const sendMessage = async () => {
    if (!input.trim() || isLoading) return;

    const userMessage: Message = {
      id: Date.now().toString(),
      content: input.trim(),
      role: 'user',
      timestamp: new Date()
    };

    setMessages(prev => [...prev, userMessage]);
    
    // Save user message to storage
    if (sessionId) {
      await db.messages.add({ ...userMessage, sessionId });
    }
    setInput('');
    setIsLoading(true);

    try {
      const response = await fetch('/api/chat', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          message: userMessage.content,
          sessionId: sessionId
        })
      });

      const data: ChatResponse = await response.json();

      if (data.error) {
        throw new Error(data.error);
      }

      // Save/update session ID to sessionStorage (per-tab, no incognito persistence)
      const currentSessionId = data.sessionId;
      if (currentSessionId) {
        // Update session ID if it's new or different (due to recovery)
        if (currentSessionId !== sessionId) {
          console.log('Session ID updated:', currentSessionId);
          setSessionId(currentSessionId);
          sessionStorage.setItem('matkompis_session_id', currentSessionId);
        }
      }

      const assistantMessage: Message = {
        id: (Date.now() + 1).toString(),
        content: data.text,
        role: 'assistant',
        timestamp: new Date(),
        canvas: data.canvas
      };

      setMessages(prev => [...prev, assistantMessage]);
      
      // Save assistant message to storage
      await db.messages.add({ ...assistantMessage, sessionId: currentSessionId });

      // Trigger canvas update if content exists
      if (data.canvas && onCanvasContent) {
        onCanvasContent(data.canvas);
      }

    } catch (error) {
      console.error('Error sending message:', error);
      const errorMessage: Message = {
        id: (Date.now() + 1).toString(),
        content: error instanceof Error ? error.message : 'Oj, något gick fel. Försök igen senare.',
        role: 'assistant',
        timestamp: new Date()
      };
      setMessages(prev => [...prev, errorMessage]);
    } finally {
      setIsLoading(false);
    }
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      sendMessage();
    }
  };



  return (
    <div className="h-full w-full bg-background flex flex-col">
      {/* Chat Header */}
      <div className="p-4 border-b border-border hidden md:block">
        <h2 className="text-lg font-semibold text-text">Matkompis</h2>
      </div>
      
      {/* Chat Messages Area */}
      <div className="flex-1 p-4 overflow-y-auto">
        {messages.length === 0 ? (
          <div className="text-muted-foreground text-center mt-8">
            <p>Hej! Jag hjälper dig med svensk matlagning och måltidsplanering.</p>
            <p className="text-sm mt-2">Fråga mig om recept, måltidsförslag eller koktekniker!</p>
          </div>
        ) : (
          <div className="space-y-4">
            {messages.map((message) => (
              <div
                key={message.id}
                className={`flex ${
                  message.role === 'user' ? 'justify-end' : 'justify-start'
                }`}
              >
                <div
                  className={`max-w-[90%] px-4 py-3 rounded-xl ${
                    message.role === 'user'
                      ? 'bg-primary text-primary-foreground ml-4'
                      : 'bg-muted mr-4'
                  }`}
                >
                  <div className="space-y-2">
                    {message.role === 'assistant' ? (
                      <div className="prose prose-sm max-w-none">
                        <ReactMarkdown 
                          components={{
                            h1: ({children}) => <h1 className="font-semibold text-2xl mb-2">{children}</h1>,
                            h2: ({children}) => <h2 className="font-semibold text-xl mb-2">{children}</h2>,
                            h3: ({children}) => <h3 className="font-semibold text-lg mb-2">{children}</h3>,
                            p: ({children}) => <p className="mb-2">{children}</p>,
                            strong: ({children}) => <strong className="font-semibold">{children}</strong>,
                            em: ({children}) => <em className="italic">{children}</em>,
                            ul: ({children}) => <ul className="list-disc list-inside mb-2">{children}</ul>,
                            ol: ({children}) => <ol className="list-decimal list-inside mb-2">{children}</ol>,
                            li: ({children}) => <li className="mb-1">{children}</li>,
                            code: ({children}) => <code className="bg-muted px-1 py-0.5 rounded text-sm">{children}</code>,
                            pre: ({children}) => <pre className="bg-muted p-3 rounded-lg overflow-x-auto text-sm mb-2">{children}</pre>
                          }}
                        >
                          {message.content}
                        </ReactMarkdown>
                      </div>
                    ) : (
                      <p className="whitespace-pre-wrap">{message.content}</p>
                    )}
                    
                    {message.canvas && (
                      <div className="mt-2 p-2 border border-border rounded-lg bg-background-alt">
                        <p className="text-sm text-muted-foreground">
                          📋 {message.canvas.data.message}
                        </p>
                      </div>
                    )}
                  </div>
                  
                  <div className="flex items-center justify-between mt-2">
                    <time className="text-xs text-muted-foreground">
                      {message.timestamp.toLocaleString('sv-SE', {
                        hour: '2-digit',
                        minute: '2-digit'
                      })}
                    </time>
                  </div>
                </div>
              </div>
            ))}
            
            {isLoading && (
              <div className="flex justify-start">
                <div className="bg-muted px-4 py-3 rounded-2xl mr-4">
                  <p className="text-muted-foreground">Tänker...</p>
                </div>
              </div>
            )}
            <div ref={messagesEndRef} />
          </div>
        )}
      </div>
      
      {/* Chat Input */}
      <div className="p-4">
        <div className="relative flex items-end">
          <textarea 
            value={input}
            onChange={(e) => setInput(e.target.value)}
            onKeyPress={handleKeyPress}
            placeholder={messages.length === 0 ? "Vad vill du ha hjälp med?" : "Svara matkompis..."}
            className="w-full min-h-[3rem] px-4 py-3 pr-14 border border-border rounded-2xl bg-background-alt text-text placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent resize-none"
            rows={2}
            disabled={isLoading}
          />
          <button 
            onClick={sendMessage}
            disabled={!input.trim() || isLoading}
            className="absolute right-2 bottom-2 p-2 bg-primary text-primary-foreground rounded-lg hover:bg-primary/90 transition-colors flex-shrink-0 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            <Send className="h-4 w-4" />
          </button>
        </div>
      </div>
    </div>
  );
}

function getCanvasTypeLabel(type: string): string {
  switch (type) {
    case 'mealPlan': return 'måltidsplan';
    case 'recipe': return 'recept';
    case 'shoppingList': return 'handlingslista';
    case 'infoBox': return 'tips';
    default: return 'information';
  }
} 