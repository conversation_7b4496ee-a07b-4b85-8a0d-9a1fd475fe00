export function isValidEmail(email: string): boolean {
  // Basic structure check
  if (!email || typeof email !== 'string') return false;
  
  // Split into local and domain parts
  const [local, domain] = email.split('@');
  if (!local || !domain) return false;
  
  // Check local part (before @)
  if (local.length > 64) return false;
  if (!/^[a-zA-Z0-9._-]+$/.test(local)) return false;
  
  // Check domain part (after @)
  if (domain.length > 255) return false;
  if (!/^[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/.test(domain)) return false;
  
  return true;
} 