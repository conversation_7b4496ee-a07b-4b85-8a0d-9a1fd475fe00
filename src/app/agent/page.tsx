'use client';

import { useState, useEffect } from 'react';
import { notFound } from 'next/navigation';
import { getProfile, setProfile, AgentProfile } from '@/lib/agentMemory';

export default function AgentPage() {
  // Check the environment variable - defaults to false if undefined
  const isAgentEnabled = process.env.NEXT_PUBLIC_TOGGLE_AGENT === 'true';
  
  if (!isAgentEnabled) {
    notFound();
  }
  
  const [response, setResponse] = useState<string>('');
  const [isLoading, setIsLoading] = useState(false);
  const [profile, setProfileState] = useState<AgentProfile | null>(null);
  const [formData, setFormData] = useState({
    familySize: 1,
    allergies: '',
    preferences: '',
  });
  const [saveMessage, setSaveMessage] = useState('');

  useEffect(() => {
    const loadProfile = async () => {
      const savedProfile = await getProfile();
      if (savedProfile) {
        setProfileState(savedProfile);
        setFormData({
          familySize: savedProfile.familySize,
          allergies: savedProfile.allergies?.join(', ') || '',
          preferences: savedProfile.preferences?.join(', ') || '',
        });
      }
    };
    loadProfile();
  }, []);

  const handleProfileSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    const newProfile: AgentProfile = {
      id: 'default',
      familySize: formData.familySize,
      allergies: formData.allergies ? formData.allergies.split(',').map(a => a.trim()) : undefined,
      preferences: formData.preferences ? formData.preferences.split(',').map(p => p.trim()) : undefined,
    };
    await setProfile(newProfile);
    setProfileState(newProfile);
    setSaveMessage('Profil sparad – redo att använda agenten!');
    setTimeout(() => setSaveMessage(''), 3000);
  };

  const handleStartAgent = async () => {
    setIsLoading(true);
    setResponse('');
    console.log('[AgentPage] Starting agent request...');
    try {
      const res = await fetch('/api/agent', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ profile }),
      });

      if (!res.ok) {
        let errorMsg = `Fel: Servern svarade med status ${res.status}`;
        try {
          const errorData = await res.json();
          errorMsg += errorData?.result ? `: ${errorData.result}` : '';
        } catch {
          // Ignore JSON parse errors
        }
        console.error('[AgentPage] API error:', errorMsg);
        setResponse(errorMsg);
        return;
      }

      let data;
      try {
        data = await res.json();
      } catch (jsonErr) {
        console.error('[AgentPage] JSON parse error:', jsonErr);
        setResponse('Fel: Kunde inte tolka svaret från servern.');
        return;
      }

      if (!data || typeof data.result !== 'string') {
        console.error('[AgentPage] Unexpected API response:', data);
        setResponse('Fel: Ovänat svar från servern.');
        return;
      }

      setResponse(data.result);
      console.log('[AgentPage] Agent request successful.');
    } catch (error) {
      console.error('[AgentPage] Network or unknown error:', error);
      setResponse('Något gick fel. Kontrollera din internetanslutning eller försök igen senare.');
    } finally {
      setIsLoading(false);
      console.log('[AgentPage] Agent request finished.');
    }
  };

  return (
    <main className="min-h-screen p-8 text-foreground">
      <div className="max-w-2xl mx-auto">
        <h1 className="text-3xl font-bold mb-8">Matkompis agent</h1>

        <form onSubmit={handleProfileSubmit} className="mb-8 space-y-4">
          <div>
            <label htmlFor="familySize" className="block mb-2">
              Hur många i familjen?
            </label>
            <input
              type="number"
              id="familySize"
              min="1"
              value={formData.familySize}
              onChange={(e) => setFormData(prev => ({ ...prev, familySize: parseInt(e.target.value) || 1 }))}
              className="w-full p-2 border rounded bg-background text-foreground"
            />
          </div>

          <div>
            <label htmlFor="allergies" className="block mb-2">
              Allergier (komma-separerade)
            </label>
            <input
              type="text"
              id="allergies"
              value={formData.allergies}
              onChange={(e) => setFormData(prev => ({ ...prev, allergies: e.target.value }))}
              className="w-full p-2 border rounded bg-background text-foreground"
              placeholder="t.ex. nötter, laktos"
            />
          </div>

          <div>
            <label htmlFor="preferences" className="block mb-2">
              Preferenser (komma-separerade)
            </label>
            <input
              type="text"
              id="preferences"
              value={formData.preferences}
              onChange={(e) => setFormData(prev => ({ ...prev, preferences: e.target.value }))}
              className="w-full p-2 border rounded bg-background text-foreground"
              placeholder="t.ex. vegetariskt, snabblagat"
            />
          </div>

          <button
            type="submit"
            className="bg-primary text-primary-foreground hover:bg-primary/90 font-semibold py-2 px-4 rounded-lg"
          >
            Spara profil
          </button>

          {saveMessage && (
            <p className="text-primary mt-2">{saveMessage}</p>
          )}
        </form>
        
        <button
          onClick={handleStartAgent}
          disabled={isLoading}
          className="bg-primary text-primary-foreground hover:bg-primary/90 font-semibold py-2 px-4 rounded-lg disabled:opacity-50 disabled:cursor-not-allowed mb-6"
        >
          {isLoading ? 'Laddar...' : 'Starta agenten'}
        </button>

        {response && (
          <div className={`p-4 rounded-lg ${response.startsWith('Fel') || response.startsWith('Något') ? 'bg-destructive/10 text-destructive' : 'bg-muted'}`}>
            <h2 className="text-lg font-semibold mb-2">Svar:</h2>
            <p>{response}</p>
          </div>
        )}
      </div>
    </main>
  );
} 