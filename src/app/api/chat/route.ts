import { NextRequest, NextResponse } from "next/server";

interface ChatRequest {
  message: string;
  sessionId?: string;
}

interface ChatResponse {
  text: string;
  canvas?: {
    type: 'mealPlan' | 'recipe' | 'shoppingList' | 'infoBox';
    data: any;
  };
  sessionId: string;
}

// Helper function to generate a unique session ID
function generateSessionId(): string {
  return `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
}

// Helper function to initialize a new session
async function initializeSession(): Promise<string> {
  const initResponse = await fetch('http://localhost:8003/session/init', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify({
      app_name: "MatkompisAgent",
      username: "matkompis_user",
    }),
    signal: AbortSignal.timeout(10000),
  });

  if (!initResponse.ok) {
    throw new Error(`Session initialization failed: ${initResponse.status}`);
  }

  const initData = await initResponse.json();
  const sessionId = initData.session_id;
  
  if (!sessionId) {
    throw new Error('Backend did not return a valid session ID');
  }
  
  return sessionId;
}

// Helper function to make a request to the MatkompisAgent backend
async function makeMatkompisRequest(sessionId: string, message: string): Promise<any> {
  const ragBackendUrl = 'http://localhost:8003/run';
  
  const backendResponse = await fetch(ragBackendUrl, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify({
      app_name: "MatkompisAgent",
      username: "matkompis_user",
      session_id: sessionId,
      query: message.trim(),
    }),
    signal: AbortSignal.timeout(30000), // 30 second timeout
  });

  if (!backendResponse.ok) {
    const errorText = await backendResponse.text();
    throw new Error(`MatkompisAgent API error: ${backendResponse.status} - ${errorText}`);
  }

  return await backendResponse.json();
}

// Helper function to check if error is a session not found error
function isSessionNotFoundError(error: any): boolean {
  return error?.message?.includes('Session not found') || 
         error?.message?.includes('session not found') ||
         (error?.message?.includes('500') && error?.message?.toLowerCase().includes('session'));
}

export async function POST(req: NextRequest) {
  try {
    const { message, sessionId }: ChatRequest = await req.json();

    // Validate required fields
    if (!message || typeof message !== 'string' || !message.trim()) {
      return NextResponse.json(
        { error: 'Meddelandet får inte vara tomt.' },
        { status: 400 }
      );
    }

    // Try to make the request (initialize session if needed)
    let actualSessionId = sessionId;
    
    // Initialize session if not provided
    if (!actualSessionId) {
      actualSessionId = await initializeSession();
    }
    
    let responseData;
    try {
      responseData = await makeMatkompisRequest(actualSessionId, message);
    } catch (error) {
      // Check if this is a session not found error
      if (isSessionNotFoundError(error)) {
        console.log('Session not found, attempting recovery...');
        
        // Attempt session recovery - initialize a new session
        try {
          console.log('Session recovered, retrying request...');
          actualSessionId = await initializeSession();
          responseData = await makeMatkompisRequest(actualSessionId, message);
        } catch (recoveryError) {
          console.error('Session recovery failed:', recoveryError);
          throw new Error('Kunde inte återställa sessionen. Försök igen senare.');
        }
      } else {
        // Not a session error, re-throw the original error
        throw error;
      }
    }

    // Extract response text from new API format
    const responseText = responseData.output || 'Ingen respons från assistenten.';
    
    // Get session ID from response
    const actualSessionIdFromResponse = responseData.session_id;
    
    if (!actualSessionIdFromResponse) {
      throw new Error('Backend did not return a valid session ID');
    }

    // Handle structured canvas data from backend
    let canvasContent = null;
    if (responseData.canvas && responseData.canvas.type && responseData.canvas.data) {
      // Use the structured canvas data directly from backend
      canvasContent = {
        type: responseData.canvas.type,
        data: responseData.canvas.data
      };
      console.log('Structured canvas data received:', responseData.canvas.type);
    } else {
      // Fallback: Simple canvas detection based on content keywords (Phase 1)
      const detectCanvasContent = (text: string) => {
        const lowerText = text.toLowerCase();
        
        if (lowerText.includes('veckomeny') || lowerText.includes('måltidsplan') || lowerText.includes('veckoplan')) {
          return {
            type: 'mealPlan' as const,
            data: {
              placeholder: true,
              text: text,
              message: 'Måltidsplanering identifierad - strukturerad data väntas från backend'
            }
          };
        }
        
        if (lowerText.includes('recept') || lowerText.includes('ingrediens')) {
          return {
            type: 'recipe' as const,
            data: {
              placeholder: true,
              text: text,
              message: 'Recept identifierat - strukturerad data väntas från backend'
            }
          };
        }
        
        return null;
      };

      canvasContent = detectCanvasContent(responseText);
    }

    const chatResponse: ChatResponse = {
      text: responseText,
      canvas: canvasContent || undefined,
      sessionId: actualSessionIdFromResponse,
    };

    return NextResponse.json(chatResponse);

  } catch (error: unknown) {
    console.error('Chat API error:', error);

    // Check if it's a timeout or network error
    if (error instanceof TypeError || (error as Error)?.name === 'AbortError') {
      return NextResponse.json(
        { error: 'Något gick fel på vår sida. Försök igen.' },
        { status: 408 }
      );
    }

    // Generic error response
    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'Vi lyckades inte, ett fel inträffade. Försök igen senare.' },
      { status: 500 }
    );
  }
} 