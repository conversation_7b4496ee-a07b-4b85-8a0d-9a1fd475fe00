import { createClient } from '@supabase/supabase-js';
import { NextResponse } from 'next/server';

const supabaseUrl = process.env.SUPABASE_URL!;
const supabaseKey = process.env.SUPABASE_ANON_KEY!;
const supabase = createClient(supabaseUrl, supabaseKey);

// Email validation regex pattern
const emailRegex = /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/;

export async function POST(request: Request) {
  try {
    const { email, password } = await request.json();

    // Improved email validation
    if (!email || !emailRegex.test(email)) {
      return NextResponse.json(
        { error: 'Vänligen kontrollera att adressen är korrekt.' },
        { status: 400 }
      );
    }

    // Check if email already exists
    const { data: existingSignup } = await supabase
      .from('early_access_signups')
      .select('email')
      .eq('email', email)
      .single();

    if (existingSignup) {
      return NextResponse.json(
        { error: 'Denna e-postadress är redan registrerad' },
        { status: 400 }
      );
    }

    // Insert new signup
    const { data, error } = await supabase
      .from('early_access_signups')
      .insert([{ email }])
      .select()
      .single();

    if (error) {
      console.error('Error saving signup:', error);
      return NextResponse.json(
        { error: 'Ett fel uppstod vid registreringen' },
        { status: 500 }
      );
    }

    return NextResponse.json({ success: true, data });
  } catch (error) {
    console.error('Unexpected error:', error);
    return NextResponse.json(
      { error: 'Ett oväntat fel uppstod' },
      { status: 500 }
    );
  }
} 