import { NextRequest, NextResponse } from "next/server";

export async function POST(req: NextRequest) {
  const { profile } = await req.json();
  const endpoint = process.env.AZURE_OPENAI_ENDPOINT!;
  const apiKey = process.env.AZURE_OPENAI_API_KEY!;
  const deployment = process.env.AZURE_OPENAI_DEPLOYMENT_ID!;
  const apiVersion = process.env.AZURE_OPENAI_API_VERSION!;

  // Use the profile data to create a prompt
  const prompt = profile
    ? `Föreslå tre middagar till en familj med följande preferenser: ${JSON.stringify(profile)}`
    : "Hej! Innan jag kan hjälpa dig behöver jag veta lite om din familjs matvanor. Hur många är ni, har ni några allergier eller preferenser?";

  const response = await fetch(`${endpoint}openai/deployments/${deployment}/chat/completions?api-version=${apiVersion}`, {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
      "api-key": apiKey,
    },
    body: JSON.stringify({
      messages: [
        { role: "system", content: "Du är Matkompis, en vänlig matplaneringsassistent." },
        { role: "user", content: prompt },
      ],
      temperature: 0.7,
    }),
  });

  const data = await response.json();

  const message = data.choices?.[0]?.message?.content ?? "Tyvärr gick något fel.";

  return NextResponse.json({ result: message });
}
