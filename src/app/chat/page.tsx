'use client';

import React, { useState, useRef } from 'react';
import { PanelLeftClose, PanelLeftOpen } from 'lucide-react';
import Chat from '@/components/Chat';
import Canvas from '@/components/Canvas';
import { ResizablePanelGroup, ResizablePanel, ResizableHandle } from '@/components/ui/resizable';
import { ImperativePanelHandle } from 'react-resizable-panels';

export default function ChatPage() {
  const [activeTab, setActiveTab] = useState<'chat' | 'canvas'>('chat');
  const [isChatCollapsed, setIsChatCollapsed] = useState(false);
  const chatPanelRef = useRef<ImperativePanelHandle>(null);

  const toggleChat = () => {
    const panel = chatPanelRef.current;
    if (panel) {
      if (panel.isCollapsed()) {
        panel.expand();
        setIsChatCollapsed(false);
      } else {
        panel.collapse();
        setIsChatCollapsed(true);
      }
    }
  };

  return (
    <div className="flex h-screen w-full">
      {/* Desktop Layout: Resizable panels */}
      <div className="hidden md:flex w-full h-full">
        <ResizablePanelGroup 
          direction="horizontal" 
          autoSaveId="chat-canvas-layout"
          className="w-full h-full"
        >
          {/* Chat Panel */}
          <ResizablePanel
            ref={chatPanelRef}
            defaultSize={45}
            minSize={25}
            maxSize={70}
            collapsible={true}
            collapsedSize={0}
            className="relative"
            onCollapse={() => setIsChatCollapsed(true)}
            onExpand={() => setIsChatCollapsed(false)}
          >
            <div className="h-full bg-background border-r border-border">
              <Chat />
              {/* Close Chat Button */}
              {!isChatCollapsed && (
                <button
                  onClick={toggleChat}
                  className="absolute top-4 right-4 z-10 p-2 bg-background border border-border rounded-md shadow-sm hover:bg-background-alt transition-colors"
                  aria-label="Stäng chat"
                >
                  <PanelLeftClose className="h-4 w-4 text-muted-foreground" />
                </button>
              )}
            </div>
          </ResizablePanel>
          
          {/* Resize Handle */}
          <ResizableHandle 
            className="w-1 bg-border hover:bg-border/80 transition-colors data-[panel-group-direction=vertical]:h-1 data-[panel-group-direction=vertical]:w-full"
            withHandle={false}
          />
          
          {/* Canvas Panel */}
          <ResizablePanel
            defaultSize={55}
            minSize={30}
            className="relative"
          >
            <div className="h-full bg-background-alt">
              <Canvas />
              {/* Open Chat Button (only visible when chat is collapsed) */}
              {isChatCollapsed && (
                <button
                  onClick={toggleChat}
                  className="absolute top-4 left-4 z-10 p-2 bg-background border border-border rounded-md shadow-sm hover:bg-background-alt transition-colors"
                  aria-label="Öppna chat"
                >
                  <PanelLeftOpen className="h-4 w-4 text-muted-foreground" />
                </button>
              )}
            </div>
          </ResizablePanel>
        </ResizablePanelGroup>
      </div>

      {/* Mobile Layout: Tabs */}
      <div className="flex flex-col md:hidden w-full h-full">
        {/* Tab Navigation */}
        <div className="flex border-b border-border bg-background">
          <button
            onClick={() => setActiveTab('chat')}
            className={`flex-1 py-3 px-4 text-center font-medium transition-colors ${
              activeTab === 'chat'
                ? 'text-primary border-b-2 border-primary bg-background-alt'
                : 'text-muted-foreground hover:text-text'
            }`}
          >
            Chat
          </button>
          <button
            onClick={() => setActiveTab('canvas')}
            className={`flex-1 py-3 px-4 text-center font-medium transition-colors ${
              activeTab === 'canvas'
                ? 'text-primary border-b-2 border-primary bg-background-alt'
                : 'text-muted-foreground hover:text-text'
            }`}
          >
            Canvas
          </button>
        </div>
        
        {/* Tab Content */}
        <div className="flex-1 overflow-hidden">
          {activeTab === 'chat' && (
            <div className="h-full">
              <Chat />
            </div>
          )}
          {activeTab === 'canvas' && (
            <div className="h-full">
              <Canvas />
            </div>
          )}
        </div>
      </div>
    </div>
  );
} 