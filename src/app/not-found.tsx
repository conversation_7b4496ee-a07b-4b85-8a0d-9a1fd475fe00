import Link from 'next/link';
import Navbar from "@/components/navbar";
import Footer from "@/components/footer";
import { But<PERSON> } from "@/components/ui/button";
import { HomeIcon } from "lucide-react";

export default function NotFound() {
  return (
    <div className="min-h-screen flex flex-col bg-background">
      <Navbar />
      <main className="flex-grow flex flex-col items-center justify-center text-center px-4 py-16">
        <div className="max-w-md space-y-6">
          <h1 className="text-6xl font-bold text-primary">404</h1>
          <h2 className="text-2xl font-semibold text-foreground">Sidan kunde inte hittas</h2>
          <p className="text-muted-foreground">
            Vi kunde tyvärr inte hitta sidan du letar efter. Den kan ha flyttats, tagits bort eller så har det råkat bli fel adress.
          </p>
          <Link href="/" className="inline-block">
            <Button className="gap-2 px-6 py-2 bg-primary text-button rounded-lg hover:bg-accent transition-colors disabled:opacity-50 whitespace-nowrap h-10">
              <HomeIcon className="h-4 w-4" />
              Till startsidan
            </Button>
          </Link>
        </div>
      </main>
      <Footer />
    </div>
  );
} 