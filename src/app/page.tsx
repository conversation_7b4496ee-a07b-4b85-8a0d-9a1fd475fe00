import Footer from "@/components/footer";
import Hero from "@/components/hero";
import Navbar from "@/components/navbar";
import {
  Users,
  Clock,
  Leaf,
  Wallet,
} from "lucide-react";
import { createClient } from "../../supabase/server";
import EmailSignupForm from "@/components/EmailSignupForm";

export default async function Home() {
  const supabase = await createClient();
  const {
    data: { user },
  } = await supabase.auth.getUser();

  return (
    <div className="min-h-screen bg-background">
      <Navbar />
      <main>
        <Hero />

        {/* Features Section */}
        <section className="py-24 bg-backgroundAlt">
          <div className="container mx-auto px-4">
            <div className="text-center mb-16">
              <h2 className="font-nunito font-black text-text text-3xl sm:text-4xl mb-4">
                Flexibel måltidsplanering
              </h2>
              <p className="text-muted-foreground max-w-2xl mx-auto">
                <PERSON><PERSON> verkty<PERSON> för planering, motivation och beteendeförändring. <PERSON><PERSON><PERSON>, plånbo<PERSON>s och planetens skull
              </p>
            </div>

            <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8">
              {[
                {
                  icon: Wallet,
                  color: 'economy',
                  title: "Sparar pengar",
                  description:
                    "Inte bara extrapriser – hjälper dig att handla billigt och smart över tid",
                },
                {
                  icon: Users,
                  color: 'idea',
                  title: "Lär känna er",
                  description:
                    "Förstår varje familjemedlems behov och preferenser",
                },
                {
                  icon: Clock,
                  color: 'health',
                  title: "Sparar tid",
                  description:
                    "Planerar veckans mat tillsammans med dig – och anpassar sig när vardagen ändras",
                },
                {
                  icon: Leaf,
                  color: 'climate',
                  title: "Hälsa + planet",
                  description:
                    "Ger vänliga puffar mot den planetära hälsodieten – eller den du föredrar",
                },
              ].map((feature, index) => {
                const Icon = feature.icon
                return (
                  <div
                    key={index}
                    className={`
                      p-6 
                      rounded-xl 
                      shadow-md 
                      hover:shadow-lg 
                      transition-shadow
                      bg-${feature.color}-surface
                    `}
                  >
                    <div className={`mb-4 text-${feature.color}`}>
                      <Icon className="w-6 h-6" />
                    </div>
                    <h3 className="font-nunito text-xl font-semibold mb-2 text-text">
                      {feature.title}
                    </h3>
                    <p className="text-muted-foreground">
                      {feature.description}
                    </p>
                  </div>
                )
              })}
            </div>
          </div>
        </section>
        <section className="py-24 bg-background">
          <div className="container mx-auto px-4 mb-12 text-center">
            {/* Heading */}
            <h2 className="text-xl sm:text-2xl font-bold mb-4">
              Vill du vara med och testa tidigt?
            </h2>
            <EmailSignupForm />
          </div>
        </section>
      </main>
      <Footer />
    </div>
  );
}
