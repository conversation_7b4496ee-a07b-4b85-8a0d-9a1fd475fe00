@tailwind base;
@tailwind components;
@tailwind utilities;



@layer base {
  /* Apply Nunito to all headings */
  h1, h2, h3, h4, h5, h6 {
    @apply font-nunito;
    text-wrap: balance;
  }

  :root {
    /* ── Custom light mode foundation ───────────────── */
    --color-text:        #091c20;
    --color-text-muted:  #4B5563;
    --color-background:  #ffffff; /*#f5f6f4;*/
    --color-background-alt:  #FDFEFD;
    --color-foreground-inv:  var(--color-background-alt);
    --color-primary:     #4C9BB6;
    --color-primary-foreground: var(--color-foreground-inv);
    --color-primary-alt: #2F5367;
    --color-secondary:   #C595D4;
    --color-accent:      #FF6B4A;



    --background: var(--color-background);
    --background-alt: var(--color-background-alt);
    --background-input: var(--color-background-alt);
    --foreground: var(--color-text);
    --primary: var(--color-primary);
    --primary-foreground: var(--color-foreground-inv);
    --secondary: var(--color-secondary);
    --secondary-foreground: var(--color-text);
    --accent: var(--color-accent);
    --accent-foreground: var(--color-foreground-inv);
    
    --button: var(--color-primary);
    --button-hover: var(--color-primary-alt);
    --button-foreground: var(--color-primary-foreground);

    --card:                var(--color-background-alt);   /* white cards */
    --popover:             0   0% 100%;   /* same as card */
    --border:             #E0E3DD;  /* light borders */
    --input:               var(--color-foreground-inv); /* white inputs */
    --ring:                0   0%   3.9%; /* for focus rings */
    
    --card-foreground:      var(--color-text);
    --popover-foreground:   var(--color-text);
    --muted: #F6F6F6; /* TODO: not pretty */
    --muted-foreground: var(--color-text-muted);
    --destructive: #FF3B30;
    --warning: #FF9500;

    --radius:              0.5rem;        /* Rounded corners */

    /* THEMED COLOURS (main + light‑mode surface tints) */
    --color-climate:        137 61%  37%;
    --color-health:         13  79%  57%;
    --color-like:           345 100% 60%;
    --color-idea:          48  92%  52%;
    --color-tool:           204 86%  53%;
    --color-economy:        279 64%  49%;
    --color-neutral:        80  10%  32%;

    --color-climate-surface: #E5FBE4;
    --color-health-surface:  #FFF0DD;
    --color-like-surface:    #FFECF2;
    --color-idea-surface:    #FFF8E5;
    --color-tool-surface:    #EEF1FF;
    --color-economy-surface: #F5F1FE;
    --color-neutral-surface: #EFF0EF;
  }

  @media (prefers-color-scheme: dark) {
    :root {
      /* ── Custom dark mode foundation ────────────────── */
      --color-text:        #F5F6F4;
      --color-text-muted:  #86928B;
      --color-background:  #091B1F;
      --color-background-alt:  #091B1F;
      --color-foreground-inv:  var(--color-background-alt);
      --color-primary:     #7AD5E6;
      --color-primary-foreground: var(--color-foreground-inv);
      --color-primary-alt:     #4C9BB6;
      --color-secondary:   #C595D4;
      --color-accent:      #C82F0D; /*#50e288;*/

      --background: var(--color-background);
      --background-alt: var(--color-background-alt);
      --background-input: var(--color-background-alt);
      --foreground: var(--color-text);
      --primary: var(--color-primary);
      --primary-foreground: #000;
      --secondary: var(--color-secondary);
      --secondary-foreground: #FFF;
      --accent: var(--color-accent);
      --accent-foreground: #000;

      --card:                #12323A;  /* dark cards */
      --card-foreground:     0   0%  98%;    /* light text on cards */
      --popover:             0   0%   3.9%;
      --border:             #2C2D4C;
      --input:               0   0%  14.9%; /* dark inputs */
      --ring:                0   0%  83.1%; /* bright focus rings */
      --muted: var(--color-secondary);
      --muted-foreground: var(--color-text-muted);
      --accent: var(--color-accent);
      --destructive: #D10A00;
      --warning: #FF9500;

      /* same main hues */
      --color-climate:        137 61%  37%;
      --color-health:         13  79%  57%;
      --color-like:           345 100% 60%;
      --color-idea:          48  92%  52%;
      --color-tool:           204 86%  53%;
      --color-economy:        279 64%  49%;
      --color-neutral:        143  8%  57%;

      /* surfaces fall back to your card color */
      --color-climate-surface: var(--card);
      --color-health-surface:  var(--card);
      --color-like-surface:    var(--card);
      --color-idea-surface:    var(--card);
      --color-tool-surface:    var(--card);
      --color-economy-surface: var(--card);
      --color-neutral-surface: var(--card);
    }
  }

  /* global resets */
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
  }

}

/* ── COMPONENTS ────────────────────────────────────────────────────────── */
@layer components {
  .nav-logo {
    height: 50px;
    width: auto;
    color: var(--color-text);
  }
  @media (max-width: 768px) {
    .nav-logo { height: 30px; }
  }

  /* Test with data URI mask */
  .ribs-mask {
    mask-image: url('data:image/svg+xml;charset=utf-8,<svg viewBox="0 0 100 100" xmlns="http://www.w3.org/2000/svg"><rect width="100" height="100" fill="black"/><circle cx="50" cy="50" r="30" fill="white"/></svg>');
    mask-size: 100% 100%;
    mask-repeat: no-repeat;
    mask-position: center;
    
    /* WebKit support */
    -webkit-mask-image: url('data:image/svg+xml;charset=utf-8,<svg viewBox="0 0 100 100" xmlns="http://www.w3.org/2000/svg"><rect width="100" height="100" fill="black"/><circle cx="50" cy="50" r="30" fill="white"/></svg>');
    -webkit-mask-size: 100% 100%;
    -webkit-mask-repeat: no-repeat;
    -webkit-mask-position: center;
  }

  /* Fallback test with CSS gradient mask */
  .ribs-mask-fallback {
    mask-image: radial-gradient(circle at center, white 30%, transparent 30%);
    -webkit-mask-image: radial-gradient(circle at center, white 30%, transparent 30%);
  }

  /* Ribs pattern using CSS gradients - segmented layout simulation */
  .ribs-pattern {
    /* Use multiple background layers to create the segmented effect */
    background-image: 
      /* Create white ribs with gaps using multiple gradients */
      /* Row 1: Full width (0-13.42%) */
      linear-gradient(to bottom, 
        transparent 0%, transparent 0%,
        white 0%, white 13.42%, 
        transparent 13.42%, transparent 14.43%,
        
        /* Row 2: Full width (14.43-27.85%) */
        white 14.43%, white 27.85%,
        transparent 27.85%, transparent 28.86%,
        
        /* Row 3: Create segments with horizontal gaps */
        white 28.86%, white 42.28%,
        transparent 42.28%, transparent 43.29%,
        
        /* Row 4: Create segments with horizontal gaps */
        white 43.29%, white 56.71%,
        transparent 56.71%, transparent 57.72%,
        
        /* Row 5: Create segments with horizontal gaps */
        white 57.72%, white 71.14%,
        transparent 71.14%, transparent 72.15%,
        
        /* Row 6: Full width (72.15-85.57%) */
        white 72.15%, white 85.57%,
        transparent 85.57%, transparent 86.58%,
        
        /* Row 7: Full width (86.58-100%) */
        white 86.58%, white 100%,
        transparent 100%
      ),
      
      /* Layer 2: Create horizontal gaps for row 3 */
      linear-gradient(to right,
        transparent 0%, transparent 68.58%, 
        black 68.58%, black 69.58%, 
        transparent 69.58%, transparent 100%
      ),
      
      /* Layer 3: Create horizontal gaps for row 4 */
      linear-gradient(to right,
        transparent 0%, transparent 42.34%, 
        black 42.34%, black 43.34%, 
        transparent 43.34%, transparent 68.58%,
        black 68.58%, black 69.58%,
        transparent 69.58%, transparent 100%
      ),
      
      /* Layer 4: Create horizontal gaps for row 5 */
      linear-gradient(to right,
        transparent 0%, transparent 30.42%, 
        black 30.42%, black 31.42%, 
        transparent 31.42%, transparent 100%
      );
      
    /* Position the layers to only affect the right rows */
    background-position: 
      0% 0%,           /* Row pattern - full height */
      0% 28.86%,       /* Row 3 gaps */
      0% 43.29%,       /* Row 4 gaps */
      0% 57.72%;       /* Row 5 gaps */
      
    background-size: 
      100% 100%,       /* Row pattern - full height */
      100% 13.42%,     /* Row 3 gaps - height of one row */
      100% 13.42%,     /* Row 4 gaps - height of one row */
      100% 13.42%;     /* Row 5 gaps - height of one row */
      
    background-repeat: no-repeat;
    
    /* Now mask this complex background */
    mask-image: linear-gradient(white, white);
    -webkit-mask-image: linear-gradient(white, white);
  }
}

/* ── UTILITIES (small helper classes) ────────────────────────────────── */
@layer utilities {
  /* always‑white button (works on <button>, <a>, etc.) */
  .text-button {
    @apply text-white !important;
  }
}
