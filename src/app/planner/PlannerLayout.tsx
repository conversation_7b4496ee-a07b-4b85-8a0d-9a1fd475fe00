"use client"
import React, { createContext, useContext, useState } from "react"
import { ChevronLeft, MessageSquare } from "lucide-react"

// Sidebar context
const SidebarContext = createContext({
  open: true,
  setOpen: (_: boolean) => {},
})

export function useSidebar() {
  return useContext(SidebarContext)
}

function SidebarProvider({ children }: { children: React.ReactNode }) {
  const [open, setOpen] = useState(true)
  return (
    <SidebarContext.Provider value={{ open, setOpen }}>
      {children}
    </SidebarContext.Provider>
  )
}

function ChatbotSidebar() {
  const { open, setOpen } = useSidebar()
  return (
    <aside
      className={`fixed md:static z-30 top-0 left-0 h-full bg-white dark:bg-zinc-900 border-r border-zinc-200 dark:border-zinc-800 shadow-md transition-all duration-200 ease-in-out
        ${open ? "w-80" : "w-0 md:w-16"} 
        ${open ? "translate-x-0" : "-translate-x-full md:translate-x-0"}
        flex flex-col`}
      style={{ minWidth: open ? 320 : 0 }}
    >
      <div className="flex items-center justify-between px-4 py-3 border-b border-zinc-100 dark:border-zinc-800 bg-white dark:bg-zinc-900">
        <span className="font-semibold text-lg text-zinc-800 dark:text-zinc-100">Matkompis</span>
        <button
          className="md:hidden p-2 rounded hover:bg-zinc-100 dark:hover:bg-zinc-800"
          onClick={() => setOpen(false)}
          aria-label="Stäng chat"
        >
          <ChevronLeft size={22} />
        </button>
      </div>
      <div className="flex-1 overflow-y-auto px-4 py-2">
        {/* Mock chat messages */}
        <div className="mb-4">
          <div className="text-xs text-zinc-400 mb-2">Matkompis:</div>
          <div className="bg-zinc-100 dark:bg-zinc-800 rounded-lg p-3 text-zinc-800 dark:text-zinc-100 mb-2">Duis aute irure dolor in reprehenderit in voluptate velit esse cillum dolore eu fugiat nulla pariatur.</div>
          <div className="bg-zinc-100 dark:bg-zinc-800 rounded-lg p-3 text-zinc-800 dark:text-zinc-100">Lorem ipsum dolor sit amet, consectetur adipiscing elit.</div>
        </div>
        <div className="mb-4">
          <div className="text-xs text-zinc-400 mb-2">Du:</div>
          <div className="bg-green-100 dark:bg-green-900 rounded-lg p-3 text-zinc-800 dark:text-zinc-100">Lorem ipsum</div>
        </div>
      </div>
      <div className="px-4 pb-4">
        <div className="flex gap-2 mb-2">
          <button className="flex-1 bg-purple-100 dark:bg-purple-900 text-purple-800 dark:text-purple-200 rounded-full px-3 py-1 text-xs font-medium">Gör billigare</button>
          <button className="flex-1 bg-green-100 dark:bg-green-900 text-green-800 dark:text-green-200 rounded-full px-3 py-1 text-xs font-medium">Mer klimatsmart</button>
          <button className="flex-1 bg-yellow-100 dark:bg-yellow-900 text-yellow-800 dark:text-yellow-200 rounded-full px-3 py-1 text-xs font-medium">Nåt nytt…</button>
        </div>
        <input
          className="w-full rounded-lg border border-zinc-200 dark:border-zinc-700 bg-zinc-50 dark:bg-zinc-800 px-3 py-2 text-sm text-zinc-900 dark:text-zinc-100 focus:outline-none focus:ring-2 focus:ring-green-400"
          placeholder="Fråga matkompis..."
        />
      </div>
    </aside>
  )
}

function SidebarToggle() {
  const { open, setOpen } = useSidebar()
  return (
    <button
      className={`fixed md:absolute z-40 top-4 left-4 md:left-0 p-2 rounded-full bg-white dark:bg-zinc-900 border border-zinc-200 dark:border-zinc-800 shadow transition-all duration-200 ${open ? "md:translate-x-80" : "md:translate-x-0"}`}
      onClick={() => setOpen(!open)}
      aria-label={open ? "Stäng chat" : "Öppna chat"}
    >
      <MessageSquare size={22} className="text-green-600 dark:text-green-400" />
    </button>
  )
}

function MainContent() {
  // Mock data
  const dinners = [
    {
      day: "Måndag",
      meals: [
        {
          name: "Spaghetti Bolognese",
          people: ["/avatars/1.png"],
          ingredients: "Spaghetti, Tomatsås, Köttfärsfräs",
        },
        {
          name: "Spaghetti Pomodoro med Mozarella",
          people: ["/avatars/2.png"],
          ingredients: "Spaghetti, Tomatsås, Mozarella",
        },
      ],
    },
    {
      day: "Tisdag",
      meals: [
        {
          name: "Bönquesadillas med Guacamole",
          people: ["/avatars/1.png", "/avatars/2.png"],
          ingredients: "Svart bönröra, Tortillabröd, Guacamole",
        },
      ],
    },
  ]
  return (
    <main className="flex-1 min-h-screen bg-zinc-50 dark:bg-zinc-950 p-6 md:p-12 transition-colors">
      <div className="max-w-3xl mx-auto">
        <div className="mb-8">
          <h2 className="text-2xl font-bold text-zinc-900 dark:text-zinc-100 mb-4">Middagar</h2>
          {dinners.map((d) => (
            <div key={d.day} className="mb-6">
              <div className="text-zinc-500 dark:text-zinc-400 font-medium mb-2">{d.day}</div>
              <div className="space-y-3">
                {d.meals.map((meal, i) => (
                  <div key={i} className="flex items-center gap-3 p-3 bg-white dark:bg-zinc-900 rounded-lg shadow-sm">
                    <div className="flex -space-x-2">
                      {meal.people.map((src, j) => (
                        <img
                          key={j}
                          src={src}
                          alt="avatar"
                          className="w-8 h-8 rounded-full border-2 border-white dark:border-zinc-900 bg-zinc-200 dark:bg-zinc-800"
                        />
                      ))}
                    </div>
                    <div>
                      <div className="font-semibold text-zinc-900 dark:text-zinc-100">{meal.name}</div>
                      <div className="text-xs text-zinc-500 dark:text-zinc-400">{meal.ingredients}</div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          ))}
        </div>
        <div className="bg-white dark:bg-zinc-900 rounded-lg shadow p-6 flex items-center justify-between">
          <div className="font-medium text-zinc-900 dark:text-zinc-100">Inköpslista <span className="text-zinc-500 dark:text-zinc-400">(849 kr)</span></div>
          <button className="text-zinc-500 dark:text-zinc-400 hover:text-zinc-900 dark:hover:text-zinc-100 transition">▼</button>
        </div>
      </div>
    </main>
  )
}

export default function PlannerLayout() {
  return (
    <SidebarProvider>
      <div className="flex min-h-screen bg-zinc-50 dark:bg-zinc-950">
        <ChatbotSidebar />
        <div className="flex-1 relative">
          <SidebarToggle />
          <MainContent />
        </div>
      </div>
    </SidebarProvider>
  )
} 