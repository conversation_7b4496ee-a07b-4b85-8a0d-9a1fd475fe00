import type { Metada<PERSON> } from "next";
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON> } from "next/font/google";
import "./globals.css";
import GoogleAnalytics from "@/components/GoogleAnalytics";
import CookieConsent from "@/components/CookieConsent";

const nunito = Nunito({ 
  subsets: ["latin"],
  variable: '--font-nunito',
});

const roboto = Roboto({ 
  weight: ['400', '500', '700'],
  subsets: ["latin"],
  variable: '--font-roboto',
});

export const metadata: Metadata = {
  title: "Matkompis – gör vardagsmaten enklare, billigare och smartare",
  description: "<PERSON><PERSON> hjälp att planera roligare, hälsosammare och billigare mat – som också är bättre för klimatet. Små steg som funkar i vardagen.",
  metadataBase: new URL('https://matkompis.se'),
  openGraph: {
    title: "Matkompis – gör vardagsmaten enklare, billigare och smartare",
    description: "<PERSON><PERSON> hjälp att planera roligare, hälsosammare och billigare mat – som också är bättre för klimatet. Små steg som funkar i vardagen.",
    url: "https://matkompis.se",
    siteName: "Matkompis",
    images: [
      {
        url: "/og.jpg",
        width: 1200,
        height: 630,
        alt: "Matkompis - Gör vardagsmaten enklare, billigare och smartare",
      },
    ],
    locale: "sv_SE",
    type: "website",
  },
  twitter: {
    card: "summary_large_image",
    title: "Matkompis – gör vardagsmaten enklare, billigare och smartare",
    description: "Få hjälp att planera roligare, hälsosammare och billigare mat – som också är bättre för klimatet. Små steg som funkar i vardagen.",
    images: ["/og.jpg"],
  },
  icons: {
    icon: [
      { url: '/favicon-96x96.png', sizes: '96x96', type: 'image/png' },
      { url: '/favicon.svg', type: 'image/svg+xml' },
      { url: '/favicon.ico', rel: 'shortcut icon' },
    ],
    apple: [
      { url: '/apple-touch-icon.png', sizes: '180x180', type: 'image/png' },
    ],
  },
  appleWebApp: {
    title: 'Matkompis',
    capable: true,
  },
  manifest: '/site.webmanifest',
  other: {
    'mobile-web-app-capable': 'yes',
  },
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="sv">
      <body className={`${roboto.className} ${roboto.variable} ${nunito.variable} font-roboto text-foreground bg-background`}>
        <GoogleAnalytics />
        {children}
        <CookieConsent />
      </body>
    </html>
  );
}
