import { notFound } from "next/navigation";
import Navbar from "@/components/navbar";
import Footer from "@/components/footer";
import MealPlannerDashboard from "@/components/MealPlannerDashboard";

export default async function DashboardPage() {
  
  // Check the environment variable - defaults to false if undefined
  const isDashboardEnabled = process.env.TOGGLE_DASHBOARD === 'true';
  if (!isDashboardEnabled) {
    notFound();
  }

  return (
    <div className="min-h-screen bg-matkompis-light">
      <Navbar />
      <main className="container mx-auto px-4 py-8">
        <MealPlannerDashboard />
      </main>
      <Footer />
    </div>
  );
}
