import { signInAction } from "@/app/actions";
import { FormMessage, Message } from "@/components/form-message";
import { SubmitButton } from "@/components/submit-button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { EmailInput } from "@/components/ui/email-input";
import Link from "next/link";

interface LoginProps {
  searchParams: Promise<Message>;
}

export default async function SignInPage({ searchParams }: LoginProps) {
  const message = await searchParams;

  if ("message" in message) {
    return (
      <div className="flex h-screen w-full flex-1 items-center justify-center p-4 sm:max-w-md">
        <FormMessage message={message} />
      </div>
    );
  }

  return (
    <div className="flex min-h-screen flex-col items-center justify-center bg-background px-4 py-8">
      <div className="w-full max-w-md rounded-lg border border-border bg-card p-6 shadow-sm">
        <form className="flex flex-col space-y-6">
          <div className="space-y-2 text-center">
            <h1 className="text-3xl font-semibold tracking-tight">Logga in</h1>
            <p className="text-sm text-muted-foreground">
              {/*
              Har du inget konto?{" "}
              <Link
                className="text-primary font-medium hover:underline transition-all"
                href="/sign-up"
              >
                Registrera dig
              </Link>*/}
              Tyvärr är det inte möjligt att skapa nya konton ännu.
            </p>
          </div>

          <div className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="email" className="text-sm font-medium">
                E-post
              </Label>
              <EmailInput
                id="email"
                name="email"
                placeholder="<EMAIL>"
                required
                className="w-full"
              />
            </div>

            <div className="space-y-2">
              <div className="flex justify-between items-center">
                <Label htmlFor="password" className="text-sm font-medium">
                  Lösenord
                </Label>
                <Link
                  className="text-xs text-muted-foreground hover:text-foreground hover:underline transition-all"
                  href="/forgot-password"
                >
                  Glömt lösenord?
                </Link>
              </div>
              <Input
                id="password"
                type="password"
                name="password"
                placeholder="Ditt lösenord"
                required
                className="w-full"
              />
            </div>
          </div>

          <SubmitButton
            className="w-full"
            pendingText="Loggar in..."
            formAction={signInAction}
          >
            Logga in
          </SubmitButton>

          <FormMessage message={message} />
        </form>
      </div>
    </div>
  );
}
