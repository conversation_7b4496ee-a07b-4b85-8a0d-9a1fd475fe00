---
description: 
globs: 
alwaysApply: false
---

# Matkompis Development Rules (Updated for NextJS TypeScript Backend)

## General

- Use TypeScript for all frontend and backend code.
- Follow the App Router structure in Next.js (`/src/app` folder).
- Keep components small and reusable.
- Only install minimal, necessary dependencies.
- Avoid overengineering—opt for simplicity.

## Backend Integration

- Use `adk-typescript` SDK to implement backend agents within the Next.js project.
- All backend logic should reside in Next.js API routes under `/src/app/api/`.
- Define and reuse agent configurations clearly in `/lib/agent.ts`.
- Use a JSON structure for API responses `{ text: string; canvas?: object }`.

### Agent Definition

- Agents must have clearly defined roles and prompt templates.
- Initially use OpenAI model endpoints, transitioning later to Gemini as appropriate.

### Example Agent Setup

Create a primary agent in `/lib/agent.ts`:

```typescript
import { Agent } from 'adk-typescript';
import { recipeSearch } from '@/tools/recipeSearch';

export const matkompisAgent = new Agent({
  promptTemplate: `Du är Matkompis, en svensk matplaneringsassistent. Svara alltid vänligt på svenska.`,
  tools: [recipeSearch]
});
```

### API Endpoint Implementation

Implement a route handler at `/src/app/api/chat/route.ts`:

```typescript
import { NextRequest, NextResponse } from 'next/server';
import { matkompisAgent } from '@/lib/agent';

export async function POST(req: NextRequest) {
  const { message } = await req.json();
  const reply = await matkompisAgent.invoke(message);
  return NextResponse.json(reply);
}
```

### Tool Integration

- Define simple tools using local data initially, such as recipe search.

Example `recipeSearch` tool:

```typescript
import recipes from '@/public/data/recipes.json';
import { Tool } from 'adk-typescript';

export const recipeSearch: Tool = {
  name: 'recipe_search',
  description: 'Hitta recept baserat på en söksträng',
  parameters: { type: 'object', properties: { query: { type: 'string' } } },
  execute: async ({ query }) =>
    recipes.filter(r => r.name.toLowerCase().includes(query.toLowerCase())).slice(0, 5)
};
```

### Canvas Integration

- Ensure backend returns structured data for canvas when appropriate:

```typescript
if (userMsg.toLowerCase().includes('vecko')) {
  return {
    text: 'Här är ett enkelt förslag!',
    canvas: {
      type: 'mealPlan',
      data: {
        days: ['Mån', 'Tis', 'Ons'],
        dinners: [
          { day: 'Mån', recipe: 'Spaghetti Bolognese' },
          // additional days...
        ]
      }
    }
  };
}
```

## Styling & Theming

- Use Tailwind CSS for all layout and styling with custom CSS variables for theming.
- Leverage the existing color system defined in `src/app/globals.css`:

  - `--color-text`, `--color-background`, `--color-primary`, etc.
- Support both light and dark modes using `@media (prefers-color-scheme: dark)`.
- Use semantic color classes like `bg-button-hover`, `text-[var(--color-text)]`.
- Never use hardcoded colors like `bg-white`, `bg-black`, `text-gray-500`, etc.
- Always use CSS variable-based classes from the theming system.

## Task Execution (CRITICAL)

- **DO ONLY what is strictly requested*- - nothing more, nothing less.
- **ASK before adding anything beyond the core requirement.**
- After implementing code, say "Please test this" rather than assuming it works.
- Wait for explicit user verification before marking tasks as completed.

## Plan Consistency

- Always align implementations with approved documentation and task lists.
- Refer to integration plans and PRDs before starting any new backend feature.

## Project Structure

- Main configuration files: `package.json`, `next.config.js`
- Reusable components in `/src/components`
- API route handlers in `/src/app/api`

## Chat & API Integration

- Frontend chat sends messages to `/api/chat`.
- Backend must consistently reply with `{ text, canvas? }`.

## Testing

- Write and run tests before implementation to cover critical paths.
- Focus on integration tests between frontend and backend API routes.


## Accessibility
- Use semantic HTML elements.
- Ensure proper color contrast ratios.
- Include appropriate ARIA labels where needed.
- Test keyboard navigation.

## Testing
- Generate unit tests before implementation where relevant.
- Only mock data that reflects real API use cases.
- Prefer integration testing of UI flows.

## Naming & Conventions
- Use camelCase for variables and functions.
- Use PascalCase for React components.
- File and folder names should be kebab-case.
- CSS custom properties use kebab-case: `--color-text-muted`.

## Project-Specific Context
- This is a Swedish food/meal planning application ("Matkompis").
- Content should be in Swedish when user-facing.
- Follow the existing design system with green/teal primary colors.
- Maintain the friendly, approachable tone of the brand.
- The logo component is in [src/components/Logo.tsx](mdc:src/components/Logo.tsx).

## Cursor-Specific
- Always follow approved planning in `prompt.md` and `workflow_state.md`.
- Always check for TypeScript errors and fix them before suggesting code.
- When modifying SVGs, ensure all attributes are React-compatible.
- Respect the existing color system and theming approach.
- Ask for clarification if task requirements are ambiguous.
- Don't generate code that spans too many responsibilities at once.
