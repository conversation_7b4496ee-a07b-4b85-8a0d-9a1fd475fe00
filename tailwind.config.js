// tailwind.config.js
/** @type {import('tailwindcss').Config} */
module.exports = {
  content: [
    './src/pages/**/*.{js,ts,jsx,tsx,mdx}',
    './src/components/**/*.{js,ts,jsx,tsx,mdx}',
    './src/app/**/*.{js,ts,jsx,tsx,mdx}',
  ],
  darkMode: 'media',
  safelist: [
    // Background colors
    {
      pattern: /^bg-(climate|health|like|idea|tool|economy|neutral)(-surface)?$/,
    },
    // Text colors
    {
      pattern: /^text-(climate|health|like|idea|tool|economy|neutral)(-surface)?$/,
    },
    // Border colors
    {
      pattern: /^border-(climate|health|like|idea|tool|economy|neutral)(-surface)?$/,
    },
    // Fill colors
    {
      pattern: /^fill-(climate|health|like|idea|tool|economy|neutral)(-surface)?$/,
    },
    // Stroke colors
    {
      pattern: /^stroke-(climate|health|like|idea|tool|economy|neutral)(-surface)?$/,
    },
  ],
  theme: {
    extend: {
      fontFamily: {
        roboto: ['var(--font-roboto)', 'sans-serif'],
        nunito: ['var(--font-nunito)', 'sans-serif'],
      },

      // ─── COLORS ──────────────────────────────────────────────────────────
      colors: {
        // Foundation
        text: 'var(--color-text)',
        background: 'var(--color-background)',
        backgroundAlt: 'var(--color-background-alt)',
        foreground: 'var(--foreground)',
        primary: 'var(--color-primary)',
        secondary: 'var(--color-secondary)',
        accent: 'var(--color-accent)',
        border: 'var(--border)',
        input: 'hsl(var(--input))',
        ring: 'hsl(var(--ring))',
        destructive: 'var(--destructive)',
        warning: 'var(--warning)',

        primary: {
          DEFAULT: 'var(--primary)',
          foreground: 'var(--primary-foreground)',
        },
        secondary: {
          DEFAULT: 'var(--secondary)',
          foreground: 'var(--secondary-foreground)',
        },
        
        muted: {
          DEFAULT: 'var(--muted)',
          foreground: 'var(--muted-foreground)',
        },
        accent: {
          DEFAULT: 'var(--accent)',
          foreground: 'var(--accent-foreground)',
        },
        popover: {
          DEFAULT: 'hsl(var(--popover))',
          foreground: 'hsl(var(--popover-foreground))',
        },
        card: {
          DEFAULT: 'var(--card)',
          foreground: 'hsl(var(--card-foreground))',
        },
        button: {
          DEFAULT: 'var(--button)',
          hover: 'var(--button-hover)',
          foreground: 'var(--button-foreground)',
        },

        // Matkompis green scale (only)
        matkompis: {
          green: {
            50: '#f0f9f1',
            100: '#dbf1de',
            200: '#b8e3c0',
            300: '#88ce97',
            400: '#57b16d',
            500: '#13723C', // your climate hue
            600: '#0d6a32',
            700: '#0d5529',
            800: '#0e4424',
            900: '#0d3920',
            950: '#072012',
          },
        },

        // Thematic tokens (singular 'idea')
        climate: 'hsl(var(--color-climate))',
        'climate-surface': 'var(--color-climate-surface)',

        health: 'hsl(var(--color-health))',
        'health-surface': 'var(--color-health-surface)',

        like: 'hsl(var(--color-like))',
        'like-surface': 'var(--color-like-surface)',

        idea: 'hsl(var(--color-idea))',
        'idea-surface': 'var(--color-idea-surface)',

        tool: 'hsl(var(--color-tool))',
        'tool-surface': 'var(--color-tool-surface)',

        economy: 'hsl(var(--color-economy))',
        'economy-surface': 'var(--color-economy-surface)',

        neutral: 'hsl(var(--color-neutral))',
        'neutral-surface': 'var(--color-neutral-surface)',
      },

      // ─── SVG UTILITIES ─────────────────────────────────────────────────
      fill: {
        primary: 'var(--color-primary)',
        text: 'var(--color-text)',
        climate: 'hsl(var(--color-climate))',
        'climate-surface': 'hsl(var(--color-climate-surface))',
        health: 'hsl(var(--color-health))',
        'health-surface': 'hsl(var(--color-health-surface))',
        like: 'hsl(var(--color-like))',
        'like-surface': 'hsl(var(--color-like-surface))',
        idea: 'hsl(var(--color-idea))',
        'idea-surface': 'hsl(var(--color-idea-surface))',
        tool: 'hsl(var(--color-tool))',
        'tool-surface': 'hsl(var(--color-tool-surface))',
        economy: 'hsl(var(--color-economy))',
        'economy-surface': 'hsl(var(--color-economy-surface))',
        neutral: 'hsl(var(--color-neutral))',
        'neutral-surface': 'hsl(var(--color-neutral-surface))',
      },
      stroke: {
        climate: 'hsl(var(--color-climate))',
        health: 'hsl(var(--color-health))',
        like: 'hsl(var(--color-like))',
        idea: 'hsl(var(--color-idea))',
        tool: 'hsl(var(--color-tool))',
        economy: 'hsl(var(--color-economy))',
        neutral: 'hsl(var(--color-neutral))',
      },

      borderRadius: {
        lg: 'var(--radius)',
        md: 'calc(var(--radius) - 2px)',
        sm: 'calc(var(--radius) - 4px)',
      },
    },
  },
  plugins: [],
}
