This is not to be implemented in this project, but in a separate API
It is here for your knowledege.

# Python Backend Implementation Plan (Google ADK + RAG)

## Overview
Build a FastAPI backend using Google Agent Development Kit (ADK) with RAG technology for intelligent recipe recommendations and dynamic user/family personalization for the Matkompis food planning application.

## High-Level Architecture

### Core Components

#### Google ADK Agent System
- **Primary Agent**: Main conversational agent for meal planning
- **Recipe RAG Agent**: Specialized agent for recipe retrieval and recommendations
- **Personalization Agent**: Handles user preferences and family profile management
- **Context Manager**: Maintains conversation history and user context

#### RAG Pipeline
- **Vector Database**: Pinecone/Weaviate for recipe embeddings
- **Knowledge Base**: Swedish recipe database with nutritional info
- **Embedding Service**: Google's text-embedding-004 model
- **Retrieval Engine**: Semantic search with filtering capabilities

#### Personalization System
- **User Profiles**: Individual dietary preferences, allergies, skills
- **Family Profiles**: Household composition, shared preferences
- **Learning Engine**: Adaptive preference extraction from conversations
- **Recommendation Engine**: Personalized meal suggestions

## Database Schema

### User Management
```sql
-- Users table
CREATE TABLE users (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    email VARCHAR(255) UNIQUE NOT NULL,
    name VARCHAR(255),
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW()
);

-- Family profiles
CREATE TABLE families (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name VARCHAR(255) NOT NULL,
    created_by UUID REFERENCES users(id),
    created_at TIMESTAMP DEFAULT NOW()
);

-- Family members
CREATE TABLE family_members (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    family_id UUID REFERENCES families(id),
    user_id UUID REFERENCES users(id) NULL, -- NULL for non-registered members
    name VARCHAR(255) NOT NULL,
    age_group VARCHAR(50), -- 'child', 'teen', 'adult', 'senior'
    relationship VARCHAR(100), -- 'parent', 'child', 'partner', etc.
    created_at TIMESTAMP DEFAULT NOW()
);
```

### Personalization Data
```sql
-- Dietary preferences and restrictions
CREATE TABLE dietary_profiles (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    family_member_id UUID REFERENCES family_members(id),
    allergies JSONB DEFAULT '[]', -- ["nötter", "laktos", "gluten"]
    dietary_type VARCHAR(100), -- 'vegetarian', 'vegan', 'keto', etc.
    dislikes JSONB DEFAULT '[]', -- ["fisk", "svamp", "broccoli"]
    preferences JSONB DEFAULT '[]', -- ["italienskt", "kryddigt", "enkelt"]
    cooking_skill VARCHAR(50), -- 'beginner', 'intermediate', 'advanced'
    time_constraints VARCHAR(50), -- 'quick', 'normal', 'elaborate'
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW()
);

-- Learned preferences over time
CREATE TABLE preference_learning (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    family_id UUID REFERENCES families(id),
    preference_type VARCHAR(100), -- 'ingredient', 'cuisine', 'cooking_method'
    preference_value VARCHAR(255),
    confidence_score FLOAT, -- 0.0 to 1.0
    learned_from VARCHAR(100), -- 'conversation', 'feedback', 'selection'
    context JSONB, -- Additional context about when/how learned
    created_at TIMESTAMP DEFAULT NOW()
);
```

### Recipe and Meal Data
```sql
-- Recipe knowledge base
CREATE TABLE recipes (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    title VARCHAR(255) NOT NULL,
    description TEXT,
    ingredients JSONB NOT NULL,
    instructions JSONB NOT NULL,
    prep_time_minutes INTEGER,
    cook_time_minutes INTEGER,
    servings INTEGER,
    difficulty VARCHAR(50),
    cuisine_type VARCHAR(100),
    dietary_tags JSONB DEFAULT '[]', -- ["vegetarian", "glutenfri"]
    nutritional_info JSONB,
    source VARCHAR(255),
    embedding_vector VECTOR(1536), -- For semantic search
    created_at TIMESTAMP DEFAULT NOW()
);

-- Meal plans generated for families
CREATE TABLE meal_plans (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    family_id UUID REFERENCES families(id),
    week_start_date DATE,
    plan_data JSONB NOT NULL, -- Complete weekly meal plan structure
    generated_by VARCHAR(100), -- 'agent', 'user_edited'
    preferences_snapshot JSONB, -- Preferences used for this plan
    created_at TIMESTAMP DEFAULT NOW()
);
```

### Conversation and Context
```sql
-- Chat sessions
CREATE TABLE chat_sessions (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    family_id UUID REFERENCES families(id),
    session_name VARCHAR(255),
    started_at TIMESTAMP DEFAULT NOW(),
    last_activity TIMESTAMP DEFAULT NOW()
);

-- Individual messages
CREATE TABLE chat_messages (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    session_id UUID REFERENCES chat_sessions(id),
    role VARCHAR(50) NOT NULL, -- 'user', 'assistant'
    content TEXT NOT NULL,
    canvas_data JSONB, -- Structured canvas content
    context JSONB, -- Agent reasoning, retrieved recipes, etc.
    created_at TIMESTAMP DEFAULT NOW()
);
```

## Implementation Phases

### Phase 1: Core Infrastructure ✅
- [ ] Set up FastAPI application with proper project structure
- [ ] Configure Google ADK SDK and authentication
- [ ] Set up PostgreSQL database with vector extension
- [ ] Implement database models with SQLAlchemy
- [ ] Create basic health check and auth middleware

### Phase 2: RAG Pipeline
- [ ] Implement recipe embedding generation
- [ ] Set up vector database (Pinecone/Weaviate)
- [ ] Create recipe ingestion pipeline
- [ ] Build semantic search functionality
- [ ] Add recipe filtering by dietary restrictions
- [ ] Implement recipe ranking based on preferences

### Phase 3: Google ADK Integration
- [ ] Configure ADK agents with Swedish language support
- [ ] Implement conversation management
- [ ] Create agent orchestration system
- [ ] Add context passing between agents
- [ ] Implement structured response generation

### Phase 4: Personalization Engine
- [ ] Build user/family profile management
- [ ] Implement preference learning algorithms
- [ ] Create recommendation scoring system
- [ ] Add feedback processing for continuous learning
- [ ] Build preference conflict resolution for families

### Phase 5: API Endpoints
- [ ] Implement main chat endpoint (`POST /api/chat`)
- [ ] Create family management endpoints
- [ ] Add meal plan CRUD operations
- [ ] Build recipe search and recommendation APIs
- [ ] Implement user preference management

### Phase 6: Advanced Features
- [ ] Add meal plan optimization algorithms
- [ ] Implement shopping list generation
- [ ] Create nutritional analysis features
- [ ] Add seasonal/local ingredient preferences
- [ ] Build batch meal planning capabilities

## API Endpoints

### Core Chat API
```python
@app.post("/api/chat")
async def chat(request: ChatRequest, family_id: str = Depends(get_family_id)):
    """
    Main chat endpoint for the canvas interface
    """
    response = await agent_orchestrator.process_message(
        message=request.message,
        family_id=family_id,
        context=await get_family_context(family_id)
    )
    return ChatResponse(
        text=response.text,
        canvas=response.canvas_data
    )
```

### Family Management
```python
@app.post("/api/families")
async def create_family(family_data: FamilyCreate):
    """Create new family profile"""

@app.get("/api/families/{family_id}/members")
async def get_family_members(family_id: str):
    """Get all family members and their preferences"""

@app.put("/api/families/{family_id}/preferences")
async def update_family_preferences(family_id: str, preferences: PreferencesUpdate):
    """Update family dietary preferences and restrictions"""
```

### Recipe and Meal Planning
```python
@app.get("/api/recipes/search")
async def search_recipes(
    query: str,
    family_id: str = Depends(get_family_id),
    limit: int = 10
):
    """Semantic search for recipes with personalization"""

@app.post("/api/meal-plans")
async def generate_meal_plan(
    family_id: str,
    preferences: MealPlanPreferences
):
    """Generate personalized weekly meal plan"""

@app.get("/api/meal-plans/{family_id}/current")
async def get_current_meal_plan(family_id: str):
    """Get active meal plan for family"""
```

## Agent Configuration

### Primary Agent (Conversation Manager)
```python
# Agent configuration for Swedish meal planning
primary_agent_config = {
    "name": "matkompis_planner",
    "instructions": """
    Du är Matkompis, en svensk matplaneringsassistent. 
    
    Dina huvuduppgifter:
    - Hjälpa familjer planera måltider för veckan
    - Föreslå recept baserat på preferenser och restriktioner
    - Skapa handlingslistor och matscheman
    - Lära dig familjens preferenser över tid
    
    Svara alltid på svenska och var hjälpsam och vänlig.
    När du föreslår måltider, använd canvas för att visa strukturerad information.
    """,
    "model": "gemini-2.0-flash-exp",
    "tools": ["recipe_search", "meal_plan_generator", "preference_updater"]
}
```

### RAG Recipe Agent
```python
recipe_agent_config = {
    "name": "recipe_specialist",
    "instructions": """
    Du är specialist på svenska recept och matlagning.
    
    Använd RAG för att:
    - Hitta relevanta recept baserat på ingredienser och preferenser
    - Föreslå substitut för ingredienser
    - Anpassa recept för olika diettyper
    - Beräkna näringsinnehåll
    
    Filtrera alltid resultat baserat på familjens allergiinformation.
    """,
    "tools": ["vector_search", "recipe_filter", "nutrition_calculator"]
}
```

## RAG Implementation Details

### Vector Database Setup
```python
# Recipe embedding and storage
class RecipeRAG:
    def __init__(self):
        self.embedder = GoogleTextEmbedding(model="text-embedding-004")
        self.vector_db = PineconeClient()
    
    async def embed_recipe(self, recipe: Recipe):
        # Create rich text representation for embedding
        recipe_text = f"""
        Recept: {recipe.title}
        Beskrivning: {recipe.description}
        Ingredienser: {', '.join(recipe.ingredients)}
        Mattyp: {recipe.cuisine_type}
        Svårighetsgrad: {recipe.difficulty}
        Kostform: {', '.join(recipe.dietary_tags)}
        """
        
        embedding = await self.embedder.embed(recipe_text)
        await self.vector_db.upsert(
            id=str(recipe.id),
            vector=embedding,
            metadata={
                "title": recipe.title,
                "cuisine": recipe.cuisine_type,
                "dietary_tags": recipe.dietary_tags,
                "prep_time": recipe.prep_time_minutes
            }
        )
    
    async def search_recipes(
        self, 
        query: str, 
        family_context: FamilyContext,
        limit: int = 10
    ):
        # Embed search query
        query_embedding = await self.embedder.embed(query)
        
        # Build filters based on family preferences
        filters = self._build_dietary_filters(family_context)
        
        # Semantic search with filters
        results = await self.vector_db.query(
            vector=query_embedding,
            filter=filters,
            top_k=limit,
            include_metadata=True
        )
        
        # Re-rank based on learned preferences
        return await self._rerank_by_preferences(results, family_context)
```

### Personalization Learning
```python
class PersonalizationEngine:
    async def learn_from_conversation(
        self, 
        message: str, 
        family_id: str,
        agent_response: str
    ):
        """Extract and store preferences from natural conversation"""
        
        # Use NLP to extract preferences
        preferences = await self._extract_preferences(message)
        
        for pref in preferences:
            await self._update_preference_score(
                family_id=family_id,
                preference_type=pref.type,
                preference_value=pref.value,
                confidence=pref.confidence,
                context={"source": "conversation", "message_snippet": message[:100]}
            )
    
    async def learn_from_selection(
        self, 
        family_id: str, 
        selected_recipe_id: str,
        context: dict
    ):
        """Learn from recipe selections and meal plan choices"""
        
        recipe = await self.db.get_recipe(selected_recipe_id)
        
        # Increase confidence for selected ingredients, cuisine, etc.
        learning_updates = [
            ("cuisine", recipe.cuisine_type, 0.1),
            ("difficulty", recipe.difficulty, 0.05),
            *[(f"ingredient_{ing}", ing, 0.05) for ing in recipe.main_ingredients]
        ]
        
        for pref_type, value, confidence_boost in learning_updates:
            await self._boost_preference_confidence(
                family_id, pref_type, value, confidence_boost
            )
```

## Performance & Optimization

### Caching Strategy
- Redis for session context and recent conversations
- Cache embeddings for frequently accessed recipes
- Precompute meal plan suggestions for active families

### Monitoring & Logging
- Track agent response times and accuracy
- Monitor RAG retrieval quality
- Log preference learning effectiveness
- Alert on personalization model drift

## Security & Privacy

### Data Protection
- Encrypt personal preference data
- Implement GDPR-compliant data deletion
- Audit logs for preference learning
- Rate limiting for API endpoints

### Authentication
- JWT tokens for session management
- Family-based access control
- API key authentication for internal services

## Success Criteria
- [ ] Sub-500ms response times for chat API
- [ ] >85% relevant recipe recommendations
- [ ] Measurable improvement in personalization over time
- [ ] Seamless integration with frontend canvas system
- [ ] Swedish language understanding and generation
- [ ] Proper handling of dietary restrictions and allergies
- [ ] Scalable to 1000+ concurrent families

## Dependencies
```requirements.txt
fastapi>=0.104.0
google-cloud-aiplatform>=1.36.0
google-generativeai>=0.3.0
sqlalchemy>=2.0.0
asyncpg>=0.29.0
pinecone-client>=2.2.0
redis>=5.0.0
pydantic>=2.4.0
python-multipart>=0.0.6
uvicorn[standard]>=0.24.0
```

This backend plan integrates seamlessly with your frontend canvas chat system while providing sophisticated RAG-powered recipe recommendations and adaptive personalization for Swedish families.