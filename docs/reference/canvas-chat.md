# Canvas Chat Implementation Plan (Extended)

## Overview
Build a chat interface with dynamic canvas rendering for the Matkompis food planning application. The chat communicates with a FastAPI backend powered by Google Agent Development Kit (ADK) agents, which can return both natural language replies and structured JSON responses.

## High-Level Architecture

### Frontend Components (Next.js + Tailwind)

#### Chat Interface
- Message thread (user and agent)
- Input field with keyboard and send button handling
- Loading state + error feedback

#### Canvas System
- Dynamic rendering of structured content based on agent reply
- Supports multiple content types
- Editable and interactive

#### Responsive Layout
- Side-by-side on desktop
- Mobile: stacked with toggleable slide-over panel

### Backend Integration
- **Endpoint**: `POST /api/chat`
- **Payload**:
```json
{
  "message": "Vad ska vi äta i veckan?"
}
```
- **Expected Response**:
```json
{
  "text": "Här är ett förslag!",
  "canvas": {
    "type": "mealPlan",
    "data": { ... }
  }
}
```
- **Agent Behavior**:
  - Returns structured canvas data when applicable
  - Always includes a natural-language reply (text)

## Implementation Phases

### Phase 1: Basic Layout ✅
- [x] Create responsive chat page layout
- [x] Implement placeholder Chat and Canvas components
- [x] Ensure mobile responsiveness

### Phase 2: Chat Functionality
- [ ] Implement message state management with `useState`
- [ ] Create `ChatMessage.tsx` component (user/assistant)
- [ ] Add input handling and send functionality
- [ ] Implement loading states
- [ ] Add error handling and feedback
- [ ] Auto-scroll to latest message

### Phase 3: API Integration
- [ ] Create API client in `/lib/api.ts`
- [ ] Implement request/response handling
- [ ] Add proper TypeScript types in `/lib/types.ts`
- [ ] Handle network errors and retries
- [ ] Debounce send input

### Phase 4: Canvas System
- [ ] Create canvas content type detection
- [ ] Implement component-based rendering:
  - [ ] `MealPlanCanvas.tsx` - Weekly meal planning
  - [ ] `ShoppingListCanvas.tsx` - Grouped ingredient list
  - [ ] `InfoBoxCanvas.tsx` - Tips, warnings, facts
  - [ ] `RecipeCanvas.tsx` - Single recipe viewer/editor
- [ ] Add canvas state management
- [ ] Implement mobile slide-over panel with toggle
- [ ] Lazy load canvas components with `dynamic()`

### Phase 5: Enhanced UX
- [ ] Add message timestamps
- [ ] Implement typing indicators
- [ ] Add message persistence (localStorage)
- [ ] Optimize performance for long conversations
- [ ] Add accessibility features (focus trap, ARIA labels)
- [ ] Canvas transitions and animations

### Phase 6: Testing & Polish
- [ ] Unit tests for components
- [ ] Integration tests for chat flow
- [ ] E2E tests for canvas rendering
- [ ] Performance optimization
- [ ] Swedish localization

## State Management
- `useState` for chat messages
- Optional: `useContext` or Zustand for shared canvas state
- Use `useEffect` to trigger scroll and animations
- Consider `useReducer` if message logic grows complex

## Canvas Content Types
```typescript
interface CanvasContent {
  type: 'mealPlan' | 'shoppingList' | 'infoBox' | 'recipe';
  data: any; // Type-specific payload
}
```

| Type | Component | Purpose |
|------|-----------|---------|
| `mealPlan` | `<MealPlanCanvas />` | Weekly meal planning |
| `shoppingList` | `<ShoppingListCanvas />` | Grouped ingredient list |
| `infoBox` | `<InfoBoxCanvas />` | Tips, warnings, facts |
| `recipe` | `<RecipeCanvas />` | Single recipe viewer/editor |

## Mobile Experience
- Slide-over panel (fixed, z-50, transition-transform)
- Toggle button on chat footer or header
- Auto-focus trap and swipe-to-close (later)
- Tap-friendly hit targets

## Folder Structure
```
/app/chat/page.tsx
/components/
  Chat.tsx
  Canvas.tsx
  messages/ChatMessage.tsx
  canvas/MealPlanCanvas.tsx
  canvas/ShoppingListCanvas.tsx
  canvas/InfoBoxCanvas.tsx
  canvas/RecipeCanvas.tsx
/lib/
  api.ts
  types.ts
```

## Performance & Optimization
- Lazy load canvas components (`dynamic()` in Next.js)
- Virtualize long message lists (future)
- Debounce send input
- Animate canvas transitions with Framer Motion (later)

## Technical Considerations

### API Integration
- Handle Swedish language input/output
- Graceful degradation when canvas data is missing
- Proper error boundaries for canvas rendering failures

### Accessibility
- Screen reader support for chat messages
- Keyboard navigation for canvas interactions
- Proper focus management in slide-over panel

### Performance
- Message virtualization for long conversations
- Canvas component lazy loading
- Optimistic UI updates for better perceived performance

## Success Criteria
- [ ] Seamless chat experience on desktop and mobile
- [ ] Dynamic canvas rendering based on agent responses
- [ ] Proper error handling and loading states
- [ ] Accessible and responsive design
- [ ] Swedish language support
- [ ] Integration with existing Matkompis design system
- [ ] Editable and interactive canvas content
- [ ] Smooth mobile slide-over panel experience

## Dependencies
- Existing Tailwind CSS setup
- TypeScript configuration
- Next.js App Router
- Existing component library (shadcn/ui)
- Optional: Framer Motion for animations
- Optional: Zustand for state management

## Notes
- Follow existing Matkompis design patterns
- Maintain Swedish language for user-facing content
- Ensure compatibility with existing color theming system
- Consider future extensibility for new canvas content types
- Agent responses should always include natural language text
- Canvas content should be editable and interactive where applicable 