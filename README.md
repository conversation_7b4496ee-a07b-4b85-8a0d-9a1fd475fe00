# Matkompis - Swedish Food Planning App

A Next.js application for Swedish food planning with AI-powered meal recommendations, built with TypeScript, Tailwind CSS, and Supabase.

## Development Setup

### Prerequisites

* **Node.js 20+** (use `.nvmrc` for version management)
* **npm 9+** or **yarn**
* **Supabase CLI** (for local database development)
* **Git**

### Quick Start

1. **Clone and install dependencies:**
   ```bash
   git clone <repository-url>
   cd matkompisNext
   npm install
   ```

2. **Set up environment variables:**
   Create a `.env.local` file in the root directory with the following variables:
   ```bash
   # Supabase (required)
   SUPABASE_URL=your_supabase_project_url
   SUPABASE_ANON_KEY=your_supabase_anon_key
   NEXT_PUBLIC_SUPABASE_URL=your_supabase_project_url
   NEXT_PUBLIC_SUPABASE_ANON_KEY=your_supabase_anon_key

   # Azure OpenAI (for AI features)
   AZURE_OPENAI_ENDPOINT=your_azure_openai_endpoint
   AZURE_OPENAI_API_KEY=your_azure_openai_api_key
   AZURE_OPENAI_DEPLOYMENT_ID=your_deployment_id
   AZURE_OPENAI_API_VERSION=2024-02-15-preview

   # Feature toggles (optional)
   TOGGLE_AUTH=true
   TOGGLE_DASHBOARD=true
   NEXT_PUBLIC_TOGGLE_AGENT=true
   ```

3. **Start local development:**
   ```bash
   npm run dev
   ```
   The app will be available at `http://localhost:3000`

### Available Scripts

* `npm run dev` - Start development server with hot reload
* `npm run build` - Build the application for production (creates standalone output)
* `npm run start` - Start production server using standalone build (for deployment, uses environment variables from the host)
* `npm run start-local` - Start production server locally with `.env.production.local` (uses dotenv-cli to load environment variables)

#### Local Production Build

To test your production build locally with production environment variables, use:

```bash
npm run build
npm run start-local
```

This will load environment variables from `.env.production.local` and start the standalone server.

### Local Database Setup (Optional)

For full local development with database features:

1. **Install Supabase CLI:**
   ```bash
   npm install -g supabase
   ```

2. **Start local Supabase:**
   ```bash
   supabase start
   ```
   This will start:
   - Database at `postgresql://postgres:postgres@127.0.0.1:54322/postgres`
   - API at `http://127.0.0.1:54321`
   - Studio at `http://127.0.0.1:54323`
   - Email testing at `http://127.0.0.1:54324`

3. **Apply migrations:**
   ```bash
   supabase db reset
   ```

### Project Structure

```
matkompisNext/
├── src/
│   ├── app/                 # Next.js 14 app router
│   │   ├── (auth)/         # Authentication pages
│   │   ├── api/            # API routes
│   │   ├── chat/           # Chat interface
│   │   ├── dashboard/      # User dashboard
│   │   └── planner/        # Meal planner
│   ├── components/         # React components
│   │   ├── ui/            # Shadcn/ui components
│   │   └── ...            # Custom components
│   ├── lib/               # Utility functions
│   └── types/             # TypeScript type definitions
├── supabase/              # Database configuration
├── public/                # Static assets
├── .github/workflows/     # GitHub Actions workflows
└── docs/                  # Documentation
```

### Technology Stack

* **Frontend:** Next.js 14, React 18, TypeScript
* **Styling:** Tailwind CSS, Shadcn/ui components
* **Database:** Supabase (PostgreSQL)
* **Authentication:** Supabase Auth
* **AI:** Azure OpenAI
* **Deployment:** GitHub Actions to Azure App Service (standalone build)

---

## Deployment

This project uses **Next.js standalone output** for optimized deployment to Azure App Service via GitHub Actions.

### Deployment Architecture

- **Build Process:** Next.js standalone build creates a self-contained application
- **Deployment:** GitHub Actions builds and deploys `.next/standalone` directory
- **Runtime:** Azure App Service runs the standalone server with minimal dependencies

### GitHub Actions Workflow

The project includes a GitHub Actions workflow (`.github/workflows/feature-new-buildpipe_matkompis-next-app.yml`) that:

1. **Builds** the Next.js application with standalone output
2. **Copies** static assets (`.next/static` and `public/`) to the standalone directory
3. **Deploys** the complete standalone build to Azure App Service

### Required GitHub Secrets

Configure these secrets in your GitHub repository:

```
SUPABASE_URL
SUPABASE_ANON_KEY
NEXT_PUBLIC_SUPABASE_URL
NEXT_PUBLIC_SUPABASE_ANON_KEY
AZURE_OPENAI_ENDPOINT
AZURE_OPENAI_API_KEY
AZURE_OPENAI_DEPLOYMENT_ID
AZURE_OPENAI_API_VERSION
AZUREAPPSERVICE_CLIENTID_63F3F45B26104DA5960BDA729E532C00
AZUREAPPSERVICE_TENANTID_7E1B95EC919B4A6BB4C1D2FD550DE82C
AZUREAPPSERVICE_SUBSCRIPTIONID_56DEF3507EDA4E2FBACF44D15D5A2B18
```

### Azure App Service Configuration

1. **App Name:** `matkompis-next-app`
2. **Runtime Stack:** Node.js 20
3. **Startup Command:** `npm start` (runs `node .next/standalone/server.js`)
4. **Environment Variables:** Set all required secrets as app settings

### Deployment Process

1. Push to `feature/new-buildpipe` branch
2. GitHub Actions automatically:
   - Installs dependencies
   - Builds the application
   - Copies static assets to standalone directory
   - Deploys to Azure App Service
3. Azure App Service starts the standalone server

### Benefits of Standalone Deployment

- **Optimized:** Only includes required dependencies
- **Fast Startup:** Minimal runtime overhead
- **Reliable:** Self-contained deployment package
- **Scalable:** Works well with Azure App Service scaling

---

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test locally with `npm run dev`
5. Submit a pull request

---

## License

This project is licensed under the MIT License.

