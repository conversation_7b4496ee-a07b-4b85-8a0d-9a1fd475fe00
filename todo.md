# Steps
[ ] Merge till Main och ändra build pipe till main
[ ] Comprimera tidigare PRD och tasks och arkivera - samt gör en ny roadmap
[ ] Byt DB till Azure
[ ] Kör adk-typescript vad ska jag äta idag

# FIX

Gör prompten bättre i API se den Todo.md i Backendprojektet, för själva vad ska jag äta idag. Gör en plan för stegen vid kallstart.


# Azure LATER
- AI Search: Pricing tier (öka när börjar testa med användare) 
- Database: Behavior when free offer limit reached - Auto-pause the database until next month

- Key Vault - Later (when you go production): You will restrict to "Selected networks" or Private Endpoint.
- start with Basic B1 (low cost, upgrade later)