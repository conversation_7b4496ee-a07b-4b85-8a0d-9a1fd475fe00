# See https://help.github.com/articles/ignoring-files/ for more about ignoring files.

# dependencies
/node_modules
/.pnp
.pnp.*
.yarn/*
!.yarn/patches
!.yarn/plugins
!.yarn/releases
!.yarn/versions

# testing
/coverage

# next.js
/.next/
/.next/cache/
/out/

# production
/build

# misc
.DS_Store
*.pem
.history

# debug
npm-debug.log*
yarn-debug.log*
yarn-error.log*
*.log

# ignore all env files
.env* 
.env*.local
.env

# testing
/test/
/cypress/
/coverage/

# vercel
.vercel

# typescript
*.tsbuildinfo
next-env.d.ts

node_modules/
**/tempobook/dynamic/
**/tempobook/storyboards/

# remove git history
.git
# .github/
yarn.lock
.nvmrc
