#!/bin/sh
echo "=== Starting Matkompis App ==="
cd /home/<USER>/wwwroot

# Clear any custom NODE_PATH that <PERSON><PERSON> might have set
unset NODE_PATH

# 1) Clean install of all dependencies (including dev dependencies needed for build)
echo "Installing all dependencies for build..."
npm ci

# 2) Build the Next.js app
echo "Building app..."
npm run build

# 3) Clean up dev dependencies after build
echo "Cleaning up dev dependencies..."
npm ci --omit=dev

# 4) Start the server on port 8080
echo "Launching server..."
PORT=${PORT:-8080}
npx next start --hostname 0.0.0.0 --port $PORT 