# Task List: Backend Integration with adk-typescript SDK

## Relevant Files

- `src/lib/agent.ts` - Centralized agent configuration and tool management
- `src/app/api/chat/route.ts` - Main chat API endpoint handler
- `src/lib/tools/shoppingList.ts` - Shopping list generation tool
- `src/lib/tools/mealPlan.ts` - Meal plan generation tool
- `src/types/agent.ts` - TypeScript type definitions for agent responses and canvas data
- `src/lib/agent.test.ts` - Unit tests for agent configuration
- `src/app/api/chat/route.test.ts` - Integration tests for chat endpoint
- `src/lib/tools/shoppingList.test.ts` - Unit tests for shopping list tool
- `src/lib/tools/mealPlan.test.ts` - Unit tests for meal plan tool
- `package.json` - Add adk-typescript dependency and migration scripts
- `src/components/Chat.tsx` - Update to use new /api/chat endpoint
- `src/lib/db.ts` - Update database connection to use Azure instead of Supabase
- `src/lib/azure-db.ts` - Azure SQL Database connection and operations
- `src/types/supabase.ts` - Update types for Azure database schema
- `scripts/migrate.ts` - Database migration script for Azure SQL Database
- `scripts/test-connection.ts` - Azure SQL Database connection test script
- `.env.example` - Environment variables template for Azure configuration
- `README.md` - Updated documentation for Azure SQL Database setup

### Notes

- Unit tests should typically be placed alongside the code files they are testing (e.g., `MyComponent.tsx` and `MyComponent.test.tsx` in the same directory).
- Use `npx jest [optional/path/to/test/file]` to run tests. Running without a path executes all tests found by the Jest configuration.

## Tasks

- [ ] 1.0 Migrate Supabase database to Azure database
  - [ ] 1.1 Set up Azure SQL Database and connection configuration
  - [ ] 1.2 Create migration scripts to transfer existing data schema
  - [ ] 1.3 Update database connection in `src/lib/db.ts`
  - [ ] 1.4 Update TypeScript types for Azure database schema
- [ ] 2.0 Set up adk-typescript SDK and project dependencies
  - [ ] 2.1 Install adk-typescript package and update package.json
  - [ ] 2.2 Configure basic SDK setup and environment variables
- [ ] 3.0 Create core agent configuration and type definitions
  - [ ] 3.1 Create agent configuration in `src/lib/agent.ts`
  - [ ] 3.2 Define TypeScript types for agent responses and canvas data
- [ ] 4.0 Create chat API endpoint with proper error handling
  - [ ] 4.1 Implement POST `/api/chat` route handler
  - [ ] 4.2 Add error handling and response formatting
  - [ ] 4.3 Test endpoint with basic message processing
- [ ] 5.0 Implement canvas data generation for meal plans and shopping lists
  - [ ] 5.1 Create meal plan generation tool
  - [ ] 5.2 Create shopping list generation tool
  - [ ] 5.3 Integrate tools with agent and test canvas rendering
- [ ] 6.0 Update frontend integration and testing
  - [ ] 6.1 Update Chat component to use new `/api/chat` endpoint
  - [ ] 6.2 Add basic unit tests for agent and tools
  - [ ] 6.3 Test end-to-end chat flow with canvas rendering 