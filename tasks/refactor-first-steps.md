Below is a pragmatic “first-steps” roadmap for **porting just enough of the Python ADK backend into your existing Next.js repo**, but this time using the **`adk-typescript`** SDK.  Everything listed can be completed with the code and data you already have in the repo, so you won’t need Pinecone, Postgres vectors, or extra infra to see it working in minutes.

---

### 1 Create a very small TypeScript **Primary Agent**

| What to copy from the Python plan                   | How to translate with `adk-typescript`                                                                                                                                                                                       |
| --------------------------------------------------- | ---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- |
| **Agent role & instructions** (“Du är Matkompis …”) | Define an `Agent` with `promptTemplate` containing the same Swedish system message.                                                                                                                                          |
| **Model choice** (`gemini-2.0-flash` in the plan)   | Start with the OpenAI chat model you already use in `/api/agent`, then swap to Gemini later.                                                                                                                                 |
| **Chat endpoint** (`POST /api/chat`)                | Implement a **new** Next.js API route at `/api/chat` that: 1) instantiates the agent, 2) passes the user’s message, 3) returns `{ text, canvas?: {} }` JSON.  Re-use the same Response schema your frontend already expects. |

**Why it’s easy**
You already have a thin Azure OpenAI proxy in `/api/agent`; replacing that function with an **ADK agent call** is mostly copy-paste plus the ADK boiler-plate.

---

### 2 Expose a **RecipeSearch tool** backed by the local JSON file

1. **Data is ready** – you ship `public/data/recipes.json` with \~170 Swedish recipes and preference scores.

2. Write a simple `RecipeSearchTool` that:

   ```ts
   import recipes from "@/public/data/recipes.json";
   import { Tool } from "adk-typescript";

   export const recipeSearch: Tool = {
     name: "recipe_search",
     description: "Hitta recept baserat på en söksträng",
     parameters: { type: "object", properties: { query: { type: "string" } } },
     // naive substring match for first demo
     execute: async ({ query }) =>
       recipes.filter(r =>
         r.name.toLowerCase().includes(query.toLowerCase())
       ).slice(0, 5)
   };
   ```

3. Register the tool on the Primary Agent:

   ```ts
   const matkompis = new Agent({
     promptTemplate,
     tools: [recipeSearch]   // just one tool to start
   });
   ```

This reproduces **the single most important capability** from the Python RAG agent (semantic search) in a form your TS frontend can call immediately—even if it’s crude substring search at first.

---

### 3 Return a **minimal “canvas” object** (Meal-plan stub)

The frontend Canvas component looks for `canvas.type === "mealPlan"` etc. Build a stub right in the agent layer:

```ts
if (userMsg.toLowerCase().includes("vecko")) {
  return {
    text: "Här är ett enkelt förslag!",
    canvas: {
      type: "mealPlan",
      data: {
        days: ["Mån", "Tis", "Ons"],
        dinners: [
          { day: "Mån", recipe: "Spaghetti Bolognese" },
          …
        ]
      }
    }
  };
}
```

No extra UI work is required—the existing `<Canvas />` will try to render it.

---

### 4 Wire it up in your Next.js project

1. **Install**

   ```bash
   pnpm add adk-typescript
   ```

2. **Create `/lib/agent.ts`** that exports the configured agent (reuse in tests).

3. **New route `src/app/api/chat/route.ts`**

   ```ts
   import { NextRequest, NextResponse } from "next/server";
   import { matkompis } from "@/lib/agent";

   export async function POST(req: NextRequest) {
     const { message } = await req.json();
     const reply = await matkompis.invoke(message);
     return NextResponse.json(reply);
   }
   ```

4. **Point the frontend**
   Change the chat client call from `/api/agent` to `/api/chat`.

---

### 5 Iterate after the smoke-test works

| Next increment                                                                             | Effort | Notes                                                          |
| ------------------------------------------------------------------------------------------ | ------ | -------------------------------------------------------------- |
| Replace substring search with **OpenAI embeddings in-memory**                              | 30 min | No Vector DB yet; just compute vectors at load-time.           |
| Add **`shopping_list_generator`** tool                                                     | 1–2 h  | Pure JS transform from selected recipes → grouped ingredients. |
| Implement **sessions** via `@supabase` in the same way the Python plan stores chat history | 2 h    | You already have Supabase in the project.                      |

---

## Why start with these 3 pieces?

* **Zero new infrastructure** – everything runs inside your Next.js app.
* **Directly exercises the adk-typescript API** without touching RAG, Pinecone, or multi-agent orchestration.
* **Hooks into the UI you already built**: Chat → Canvas pathway is exactly what Phase 2/3 of your frontend plan needs .

Once this baseline works, moving the richer Python features (vector DB, preference learning, multi-agent routing) becomes a matter of swapping implementations behind the same tool names.

Good luck, and ping me when you want to deepen the RAG side!
