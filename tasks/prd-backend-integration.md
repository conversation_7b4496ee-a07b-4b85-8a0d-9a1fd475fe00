# Product Requirements Document: Backend Integration with adk-typescript SDK

## Introduction/Overview

This PRD outlines the implementation of a TypeScript-based backend integration for Matkompis using the `adk-typescript` SDK within the existing Next.js project. The primary goal is to replace the current Python `/api/agent` endpoint with a native TypeScript solution that provides chat functionality, recipe search capabilities, and structured canvas data responses. This integration will simplify the backend infrastructure, improve maintainability, and enable faster iteration cycles while maintaining compatibility with the existing frontend components.

## Goals

1. **Replace Python Backend**: Completely replace the existing Python `/api/agent` endpoint with a TypeScript implementation using `adk-typescript` SDK
2. **Maintain API Compatibility**: Ensure the new `/api/chat` endpoint returns the same JSON structure (`{ text: string; canvas?: object }`) expected by existing frontend components
3. **Implement Core Tools**: Provide recipe search functionality backed by local JSON data (`recipes.json`)
4. **Support Canvas Data**: Generate structured canvas data for meal plans, shopping lists, and recipe displays
5. **Performance Optimization**: Achieve consistent response times under 1 second
6. **Future-Ready Architecture**: Design the system to support future extensions like multiple agent types and vector database integration

Here's your updated **User Stories** section, clearly reflecting your priority for today's meal suggestions:

## User Stories

1. **Today's Meal Suggestions (Priority)**:
   *"As a user, I want immediate suggestions for what to eat today. If I’m new and the system has no prior knowledge of my preferences or available ingredients, Matkompis should quickly provide three ultra-fast meal ideas and two slightly more involved but still quick options. It should then ask simple follow-up questions to refine future suggestions."*

2. **Recipe Discovery**:
   *"As a user, I want to ask for recipe suggestions based on ingredients I have, so I can quickly plan my meals."*

3. **Visual Meal Planning**:
   *"As a user, I want to see a structured visual meal plan, making weekly meal planning straightforward and intuitive."*

4. **Shopping List Generation**:
   *"As a user, I want to generate a shopping list automatically based on selected recipes, simplifying grocery shopping."*

5. **Seamless Integration**:
   *"As a user, I want the chat interface to work exactly as before, so I don't need to learn new interactions."*


## Functional Requirements

1. **API Endpoint Implementation**: The system must provide a `POST /api/chat` endpoint that accepts user messages and returns structured JSON responses
2. **Agent Configuration**: The system must implement a primary Matkompis agent with Swedish language instructions and appropriate prompt templates
3. **Recipe Search Tool**: The system must provide a recipe search tool that queries the local `recipes.json` file and returns relevant results
4. **Canvas Data Generation**: The system must generate structured canvas data for meal plans, shopping lists, and recipe displays
5. **Response Format Compliance**: The system must return responses in the format `{ text: string; canvas?: object }` to maintain frontend compatibility
6. **Error Handling**: The system must handle API errors gracefully and return appropriate error responses
7. **Performance Monitoring**: The system must respond within 1 second for typical requests
8. **Tool Registration**: The system must support registering multiple tools with the primary agent
9. **Message Processing**: The system must process user messages and route them to appropriate tools based on content
10. **Canvas Type Support**: The system must support multiple canvas types including "mealPlan", "shoppingList", and "recipe"

## Non-Goals (Out of Scope)

1. **User Authentication**: This integration will not handle user authentication (managed by existing `(auth)` routes)
2. **Chat History Persistence**: The system will not persist chat history in the initial implementation
3. **External Database Integration**: No vector databases, RAG solutions, or external database connections in the initial phase
4. **Real-time Features**: No WebSocket connections or real-time chat features
5. **Multi-user Scalability**: The system is designed for single-user/family scenarios, not enterprise-level concurrent usage
6. **Advanced AI Features**: No complex multi-agent orchestration or advanced AI capabilities beyond basic chat and tool usage

## Design Considerations

- **Component Reuse**: Leverage existing frontend components like `<Canvas />` and `<Chat />` without modifications
- **Styling Consistency**: Follow the existing Tailwind CSS theming system and color variables defined in `globals.css`
- **TypeScript Integration**: Ensure full TypeScript support with proper type definitions for all API responses
- **Error Boundaries**: Implement appropriate error handling that maintains user experience
- **Loading States**: Consider loading states for tool execution and API responses

## Technical Considerations

- **SDK Integration**: Use `adk-typescript` SDK for agent implementation and tool management
- **File Structure**: Follow the existing Next.js App Router structure with API routes in `/src/app/api/`
- **Agent Configuration**: Create a centralized agent configuration in `/lib/agent.ts` for reusability
- **Tool Implementation**: Implement tools as simple TypeScript functions that can be easily tested and extended
- **Response Schema**: Maintain compatibility with existing frontend expectations for canvas data structure
- **Performance**: Optimize for sub-1-second response times through efficient tool execution and minimal overhead

## Success Metrics

1. **API Response Time**: 95% of requests respond within 1 second
2. **Frontend Compatibility**: 100% of existing frontend components work without modifications
3. **Recipe Search Accuracy**: Recipe search returns relevant results for 90% of user queries
4. **Canvas Rendering**: Canvas data renders correctly in 100% of test cases
5. **Error Rate**: API error rate remains below 1% for typical usage patterns
6. **Development Velocity**: Ability to add new tools within 30 minutes of development time

## Open Questions

1. **Tool Prioritization**: Should the system prioritize certain tools over others when multiple tools could handle a user request?
2. **Canvas Data Complexity**: What is the maximum complexity level for canvas data structures that should be supported?
3. **Tool Execution Order**: Should tools execute sequentially or in parallel when multiple tools are triggered?
4. **Fallback Behavior**: What should happen when no tools can handle a user request?
5. **Tool Configuration**: Should tool parameters be configurable at runtime or only at build time?
6. **Testing Strategy**: What level of unit and integration testing is required for the agent and tools?

## Implementation Phases

### Phase 1: Core Integration
- Set up `adk-typescript` SDK
- Implement basic agent with Swedish prompt template
- Create `/api/chat` endpoint
- Implement recipe search tool

### Phase 2: Canvas Support
- Add canvas data generation for meal plans
- Implement shopping list tool
- Add recipe display canvas support

### Phase 3: Optimization
- Performance tuning
- Error handling improvements
- Tool execution optimization

### Phase 4: Future Preparation
- Architecture preparation for multiple agent types
- Foundation for vector database integration
- Extensibility improvements 