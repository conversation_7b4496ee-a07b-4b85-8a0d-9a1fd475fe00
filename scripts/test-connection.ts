#!/usr/bin/env tsx

import { getPool, closePool, executeQuery } from '../src/lib/azure-db';

/**
 * Test Azure SQL Database connection
 */
async function testConnection() {
  console.log('🔍 Testing Azure SQL Database connection...');

  try {
    // Test basic connection
    console.log('📡 Connecting to database...');
    const pool = await getPool();
    console.log('✅ Connection established successfully!');

    // Test query execution
    console.log('🔍 Testing query execution...');
    const result = await executeQuery('SELECT GETDATE() as current_time, @@VERSION as version');
    console.log('✅ Query executed successfully!');
    console.log('📊 Server time:', result.recordset[0].current_time);
    console.log('📊 SQL Server version:', result.recordset[0].version.split('\n')[0]);

    // Test table existence
    console.log('🔍 Checking table structure...');
    const tables = await executeQuery(`
      SELECT TABLE_NAME 
      FROM INFORMATION_SCHEMA.TABLES 
      WHERE TABLE_TYPE = 'BASE TABLE'
      ORDER BY TABLE_NAME
    `);
    
    console.log('📊 Available tables:');
    tables.recordset.forEach(table => {
      console.log(`  - ${table.TABLE_NAME}`);
    });

    console.log('🎉 Database connection test completed successfully!');

  } catch (error) {
    console.error('❌ Connection test failed:', error);
    
    if (error instanceof Error) {
      if (error.message.includes('Login failed')) {
        console.error('💡 Check your username and password in environment variables');
      } else if (error.message.includes('Cannot open server')) {
        console.error('💡 Check your server name and firewall settings');
      } else if (error.message.includes('Cannot open database')) {
        console.error('💡 Check your database name in environment variables');
      }
    }
    
    process.exit(1);
  } finally {
    await closePool();
  }
}

// Run test if this script is executed directly
if (require.main === module) {
  testConnection().catch(console.error);
}

export { testConnection };
