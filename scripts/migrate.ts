#!/usr/bin/env tsx

import { executeQuery, closePool } from '../src/lib/azure-db';
import * as fs from 'fs';
import * as path from 'path';

/**
 * Run database migrations for Azure SQL Database
 */
async function runMigrations() {
  console.log('🚀 Starting Azure SQL Database migrations...');

  try {
    // Create users table
    console.log('📝 Creating users table...');
    await executeQuery(`
      IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='users' AND xtype='U')
      CREATE TABLE users (
        id UNIQUEIDENTIFIER PRIMARY KEY NOT NULL,
        avatar_url NVARCHAR(MAX),
        user_id NVARCHAR(255) UNIQUE,
        token_identifier NVARCHAR(255) NOT NULL,
        image NVARCHAR(MAX),
        created_at DATETIME2 NOT NULL DEFAULT GETUTCDATE(),
        updated_at DATETIME2,
        email NVARCHAR(255),
        name NVA<PERSON><PERSON><PERSON>(255),
        full_name NVARCHAR(255)
      );
    `);

    // Create indexes for better performance
    console.log('📝 Creating indexes...');
    await executeQuery(`
      IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_users_email')
      CREATE INDEX IX_users_email ON users(email);
    `);

    await executeQuery(`
      IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_users_user_id')
      CREATE INDEX IX_users_user_id ON users(user_id);
    `);

    // Create chat_sessions table for storing chat history
    console.log('📝 Creating chat_sessions table...');
    await executeQuery(`
      IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='chat_sessions' AND xtype='U')
      CREATE TABLE chat_sessions (
        id UNIQUEIDENTIFIER PRIMARY KEY DEFAULT NEWID(),
        user_id UNIQUEIDENTIFIER,
        session_id NVARCHAR(255) NOT NULL,
        created_at DATETIME2 NOT NULL DEFAULT GETUTCDATE(),
        updated_at DATETIME2,
        FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
      );
    `);

    // Create messages table for storing individual messages
    console.log('📝 Creating messages table...');
    await executeQuery(`
      IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='messages' AND xtype='U')
      CREATE TABLE messages (
        id BIGINT IDENTITY(1,1) PRIMARY KEY,
        session_id NVARCHAR(255) NOT NULL,
        role NVARCHAR(50) NOT NULL,
        content NVARCHAR(MAX) NOT NULL,
        canvas_data NVARCHAR(MAX),
        timestamp DATETIME2 NOT NULL DEFAULT GETUTCDATE(),
        INDEX IX_messages_session_id (session_id),
        INDEX IX_messages_timestamp (timestamp)
      );
    `);

    // Create user_profiles table for storing user preferences
    console.log('📝 Creating user_profiles table...');
    await executeQuery(`
      IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='user_profiles' AND xtype='U')
      CREATE TABLE user_profiles (
        id UNIQUEIDENTIFIER PRIMARY KEY DEFAULT NEWID(),
        user_id UNIQUEIDENTIFIER UNIQUE NOT NULL,
        family_size INT DEFAULT 1,
        allergies NVARCHAR(MAX), -- JSON array as string
        preferences NVARCHAR(MAX), -- JSON array as string
        created_at DATETIME2 NOT NULL DEFAULT GETUTCDATE(),
        updated_at DATETIME2,
        FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
      );
    `);

    console.log('✅ All migrations completed successfully!');
    console.log('📊 Database schema is ready for use.');

  } catch (error) {
    console.error('❌ Migration failed:', error);
    process.exit(1);
  } finally {
    await closePool();
  }
}

// Run migrations if this script is executed directly
if (require.main === module) {
  runMigrations().catch(console.error);
}

export { runMigrations };
