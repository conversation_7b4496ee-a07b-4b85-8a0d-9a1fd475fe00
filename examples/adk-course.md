This file is a merged representation of the entire codebase, combined into a single document by Repomix.
The content has been processed where comments have been removed, security check has been disabled.

# File Summary

## Purpose
This file contains a packed representation of the entire repository's contents.
It is designed to be easily consumable by AI systems for analysis, code review,
or other automated processes.

## File Format
The content is organized as follows:
1. This summary section
2. Repository information
3. Directory structure
4. Repository files (if enabled)
5. Multiple file entries, each consisting of:
  a. A header with the file path (## File: path/to/file)
  b. The full contents of the file in a code block

## Usage Guidelines
- This file should be treated as read-only. Any changes should be made to the
  original repository files, not this packed version.
- When processing this file, use the file path to distinguish
  between different files in the repository.
- Be aware that this file may contain sensitive information. Handle it with
  the same level of security as you would the original repository.

## Notes
- Some files may have been excluded based on .gitignore rules and Repomix's configuration
- Binary files are not included in this packed representation. Please refer to the Repository Structure section for a complete list of file paths, including binary files
- Files matching patterns in .gitignore are excluded
- Files matching default ignore patterns are excluded
- Code comments have been removed from supported file types
- Security check has been disabled - content may contain sensitive information
- Files are sorted by Git change count (files with more changes are at the bottom)

# Directory Structure
```
1-basic-agent/
  greeting_agent/
    __init__.py
    .env.example
    agent.py
  README.md
10-sequential-agent/
  lead_qualification_agent/
    subagents/
      recommender/
        __init__.py
        agent.py
      scorer/
        __init__.py
        agent.py
      validator/
        __init__.py
        agent.py
      __init__.py
    __init__.py
    .env.example
    agent.py
  README.md
11-parallel-agent/
  system_monitor_agent/
    subagents/
      cpu_info_agent/
        __init__.py
        agent.py
        tools.py
      disk_info_agent/
        __init__.py
        agent.py
        tools.py
      memory_info_agent/
        __init__.py
        agent.py
        tools.py
      synthesizer_agent/
        __init__.py
        agent.py
      __init__.py
    __init__.py
    .env.example
    agent.py
  README.md
12-loop-agent/
  linkedin_post_agent/
    subagents/
      post_generator/
        __init__.py
        agent.py
      post_refiner/
        __init__.py
        agent.py
      post_reviewer/
        __init__.py
        agent.py
        tools.py
      __init__.py
    __init__.py
    agent.py
  README.md
2-tool-agent/
  tool_agent/
    __init__.py
    .env.example
    agent.py
  README.md
3-litellm-agent/
  dad_joke_agent/
    __init__.py
    .env.example
    agent.py
  README.md
4-structured-outputs/
  email_agent/
    __init__.py
    .env.example
    agent.py
  README.md
5-sessions-and-state/
  question_answering_agent/
    __init__.py
    .env.example
    agent.py
  basic_stateful_session.py
  README.md
6-persistent-storage/
  memory_agent/
    __init__.py
    agent.py
  main.py
  README.md
  utils.py
7-multi-agent/
  manager/
    sub_agents/
      funny_nerd/
        agent.py
      news_analyst/
        agent.py
      stock_analyst/
        agent.py
    tools/
      tools.py
    __init__.py
    agent.py
  README.md
8-stateful-multi-agent/
  customer_service_agent/
    sub_agents/
      course_support_agent/
        __init__.py
        agent.py
      order_agent/
        agent.py
      policy_agent/
        __init__.py
        agent.py
      sales_agent/
        __init__.py
        agent.py
    __init__.py
    agent.py
  main.py
  README.md
  utils.py
9-callbacks/
  before_after_agent/
    __init__.py
    .env.example
    agent.py
  before_after_model/
    __init__.py
    .env.example
    agent.py
  before_after_tool/
    __init__.py
    .env.example
    agent.py
  README.md
.gitignore
GEMINI_MODELS.md
README.md
requirements.txt
```

# Files

## File: 1-basic-agent/greeting_agent/__init__.py
````python
from . import agent
````

## File: 1-basic-agent/greeting_agent/.env.example
````
GOOGLE_GENAI_USE_VERTEXAI=FALSE
GOOGLE_API_KEY=...
````

## File: 1-basic-agent/greeting_agent/agent.py
````python
from google.adk.agents import Agent

root_agent = Agent(
    name="greeting_agent",

    model="gemini-2.0-flash",
    description="Greeting agent",
    instruction="""
    You are a helpful assistant that greets the user.
    Ask for the user's name and greet them by name.
    """,
)
````

## File: 1-basic-agent/README.md
````markdown
# Basic ADK Agent Example

## What is an ADK Agent?

The `LlmAgent` (often aliased simply as `Agent`) is a core component in ADK that acts as the "thinking" part of your application. It leverages the power of a Large Language Model (LLM) for:
- Reasoning
- Understanding natural language
- Making decisions
- Generating responses
- Interacting with tools

Unlike deterministic workflow agents that follow predefined paths, an `LlmAgent`'s behavior is non-deterministic. It uses the LLM to interpret instructions and context, deciding dynamically how to proceed, which tools to use (if any), or whether to transfer control to another agent.

## Required Agent Structure

For ADK to discover and run your agents properly (especially with `adk web`), your project must follow a specific structure:

```
parent_folder/
    agent_folder/         # This is your agent's package directory
        __init__.py       # Must import agent.py
        agent.py          # Must define root_agent
        .env              # Environment variables
```

### Essential Components:

1. **`__init__.py`**
   - Must import the agent module: `from . import agent`
   - This makes your agent discoverable by ADK

2. **`agent.py`**
   - Must define a variable named `root_agent`
   - This is the entry point that ADK uses to find your agent

3. **Command Location**
   - Always run `adk` commands from the parent directory, not from inside the agent directory
   - Example: Run `adk web` from the parent folder that contains your agent folder

This structure ensures that ADK can automatically discover and load your agent when running commands like `adk web` or `adk run`.

## Key Components

### 1. Identity (`name` and `description`)
- **name** (Required): A unique string identifier for your agent
- **description** (Optional, but recommended): A concise summary of the agent's capabilities. Used for other agents to determine if they should route a task to this agent.

### 2. Model (`model`)
- Specifies which LLM powers the agent (e.g., "gemini-2.0-flash")
- Affects the agent's capabilities, cost, and performance

### 3. Instructions (`instruction`)
The most critical parameter for shaping your agent's behavior. It defines:
- Core task or goal
- Personality or persona
- Behavioral constraints
- How to use available tools
- Desired output format

### 4. Tools (`tools`)
Optional capabilities beyond the LLM's built-in knowledge, allowing the agent to:
- Interact with external systems
- Perform calculations
- Fetch real-time data
- Execute specific actions

## Getting Started

This example uses the same virtual environment created in the root directory. Make sure you have:

1. Activated the virtual environment from the root directory:
```bash
# macOS/Linux:
source ../.venv/bin/activate
# Windows CMD:
..\.venv\Scripts\activate.bat
# Windows PowerShell:
..\.venv\Scripts\Activate.ps1
```

2. Set up your API key:
   - Rename `.env.example` to `.env` in the greeting_agent folder
   - Add your Google API key to the `GOOGLE_API_KEY` variable in the `.env` file

## Running the Example

To run this basic agent example, you'll use the ADK CLI tool which provides several ways to interact with your agent:

1. Navigate to the 1-basic-agent directory containing your agent folder.
2. Start the interactive web UI:
```bash
adk web
```

3. Access the web UI by opening the URL shown in your terminal (typically http://localhost:8000)

4. Select your agent from the dropdown menu in the top-left corner of the UI

5. Start chatting with your agent in the textbox at the bottom of the screen

### Troubleshooting

If your agent doesn't appear in the dropdown menu:
- Make sure you're running `adk web` from the parent directory (1-basic-agent), not from inside the agent directory
- Check that your `__init__.py` properly imports the agent module
- Verify that `agent.py` defines a variable named `root_agent`

### Alternative Run Methods

The ADK CLI tool provides several options:

- **`adk web`**: Launches an interactive web UI for testing your agent with a chat interface
- **`adk run [agent_name]`**: Runs your agent directly in the terminal
- **`adk api_server`**: Starts a FastAPI server to test API requests to your agent

### Example Prompts to Try

- "How do you say hello in Spanish?"
- "What's a formal greeting in Japanese?"
- "Tell me how to greet someone in French"

You can exit the conversation or stop the server by pressing `Ctrl+C` in your terminal.

This example demonstrates a simple agent that responds to greeting-related queries, showing the fundamentals of agent creation with ADK.
````

## File: 10-sequential-agent/lead_qualification_agent/subagents/recommender/__init__.py
````python
from .agent import action_recommender_agent
````

## File: 10-sequential-agent/lead_qualification_agent/subagents/recommender/agent.py
````python
from google.adk.agents import LlmAgent


GEMINI_MODEL = "gemini-2.0-flash"


action_recommender_agent = LlmAgent(
    name="ActionRecommenderAgent",
    model=GEMINI_MODEL,
    instruction="""You are an Action Recommendation AI.

    Based on the lead information and scoring:

    - For invalid leads: Suggest what additional information is needed
    - For leads scored 1-3: Suggest nurturing actions (educational content, etc.)
    - For leads scored 4-7: Suggest qualifying actions (discovery call, needs assessment)
    - For leads scored 8-10: Suggest sales actions (demo, proposal, etc.)

    Format your response as a complete recommendation to the sales team.

    Lead Score:
    {lead_score}

    Lead Validation Status:
    {validation_status}
    """,
    description="Recommends next actions based on lead qualification.",
    output_key="action_recommendation",
)
````

## File: 10-sequential-agent/lead_qualification_agent/subagents/scorer/__init__.py
````python
from .agent import lead_scorer_agent
````

## File: 10-sequential-agent/lead_qualification_agent/subagents/scorer/agent.py
````python
from google.adk.agents import LlmAgent


GEMINI_MODEL = "gemini-2.0-flash"


lead_scorer_agent = LlmAgent(
    name="LeadScorerAgent",
    model=GEMINI_MODEL,
    instruction="""You are a Lead Scoring AI.

    Analyze the lead information and assign a qualification score from 1-10 based on:
    - Expressed need (urgency/clarity of problem)
    - Decision-making authority
    - Budget indicators
    - Timeline indicators

    Output ONLY a numeric score and ONE sentence justification.

    Example output: '8: Decision maker with clear budget and immediate need'
    Example output: '3: Vague interest with no timeline or budget mentioned'
    """,
    description="Scores qualified leads on a scale of 1-10.",
    output_key="lead_score",
)
````

## File: 10-sequential-agent/lead_qualification_agent/subagents/validator/__init__.py
````python
from .agent import lead_validator_agent
````

## File: 10-sequential-agent/lead_qualification_agent/subagents/validator/agent.py
````python
from google.adk.agents import LlmAgent


GEMINI_MODEL = "gemini-2.0-flash"


lead_validator_agent = LlmAgent(
    name="LeadValidatorAgent",
    model=GEMINI_MODEL,
    instruction="""You are a Lead Validation AI.

    Examine the lead information provided by the user and determine if it's complete enough for qualification.
    A complete lead should include:
    - Contact information (name, email or phone)
    - Some indication of interest or need
    - Company or context information if applicable

    Output ONLY 'valid' or 'invalid' with a single reason if invalid.

    Example valid output: 'valid'
    Example invalid output: 'invalid: missing contact information'
    """,
    description="Validates lead information for completeness.",
    output_key="validation_status",
)
````

## File: 10-sequential-agent/lead_qualification_agent/subagents/__init__.py
````python
from . import recommender, scorer, validator
````

## File: 10-sequential-agent/lead_qualification_agent/__init__.py
````python
from . import agent
````

## File: 10-sequential-agent/lead_qualification_agent/.env.example
````
GOOGLE_GENAI_USE_VERTEXAI=FALSE
GOOGLE_API_KEY=your_api_key_here
````

## File: 10-sequential-agent/lead_qualification_agent/agent.py
````python
from google.adk.agents import SequentialAgent

from .subagents.recommender import action_recommender_agent
from .subagents.scorer import lead_scorer_agent


from .subagents.validator import lead_validator_agent


root_agent = SequentialAgent(
    name="LeadQualificationPipeline",
    sub_agents=[lead_validator_agent, lead_scorer_agent, action_recommender_agent],
    description="A pipeline that validates, scores, and recommends actions for sales leads",
)
````

## File: 10-sequential-agent/README.md
````markdown
# Sequential Agents in ADK

This example demonstrates how to implement a Sequential Agent in the Agent Development Kit (ADK). The main agent in this example, `lead_qualification_agent`, is a Sequential Agent that executes sub-agents in a predefined order, with each agent's output feeding into the next agent in the sequence.

## What are Sequential Agents?

Sequential Agents are workflow agents in ADK that:

1. **Execute in a Fixed Order**: Sub-agents run one after another in the exact sequence they are specified
2. **Pass Data Between Agents**: Using state management to pass information from one sub-agent to the next
3. **Create Processing Pipelines**: Perfect for scenarios where each step depends on the previous step's output

Use Sequential Agents when you need a deterministic, step-by-step workflow where the execution order matters.

## Lead Qualification Pipeline Example

In this example, we've created `lead_qualification_agent` as a Sequential Agent that implements a lead qualification pipeline for sales teams. This Sequential Agent orchestrates three specialized sub-agents:

1. **Lead Validator Agent**: Checks if the lead information is complete enough for qualification
   - Validates for required information like contact details and interest
   - Outputs a simple "valid" or "invalid" with a reason

2. **Lead Scorer Agent**: Scores valid leads on a scale of 1-10
   - Analyzes factors like urgency, decision-making authority, budget, and timeline
   - Provides a numeric score with a brief justification

3. **Action Recommender Agent**: Suggests next steps based on the validation and score
   - For invalid leads: Recommends what information to gather
   - For low-scoring leads (1-3): Suggests nurturing actions
   - For medium-scoring leads (4-7): Suggests qualifying actions
   - For high-scoring leads (8-10): Suggests sales actions

### How It Works

The `lead_qualification_agent` Sequential Agent orchestrates this process by:

1. Running the Validator first to determine if the lead is complete
2. Running the Scorer next (which can access validation results via state)
3. Running the Recommender last (which can access both validation and scoring results)

The output of each sub-agent is stored in the session state using the `output_key` parameter:
- `validation_status`
- `lead_score`
- `action_recommendation`

## Project Structure

```
9-sequential-agent/
│
├── lead_qualification_agent/       # Main Sequential Agent package
│   ├── __init__.py                 # Package initialization
│   ├── agent.py                    # Sequential Agent definition (root_agent)
│   │
│   └── subagents/                  # Sub-agents folder
│       ├── __init__.py             # Sub-agents initialization
│       │
│       ├── validator/              # Lead validation agent
│       │   ├── __init__.py
│       │   └── agent.py
│       │
│       ├── scorer/                 # Lead scoring agent
│       │   ├── __init__.py
│       │   └── agent.py
│       │
│       └── recommender/            # Action recommendation agent
│           ├── __init__.py
│           └── agent.py
│
├── .env.example                    # Environment variables example
└── README.md                       # This documentation
```

## Getting Started

### Setup

1. Activate the virtual environment from the root directory:
```bash
# macOS/Linux:
source ../.venv/bin/activate
# Windows CMD:
..\.venv\Scripts\activate.bat
# Windows PowerShell:
..\.venv\Scripts\Activate.ps1
```

2. Copy the `.env.example` file to `.env` and add your Google API key:
```
GOOGLE_API_KEY=your_api_key_here
```

### Running the Example

```bash
cd 9-sequential-agent
adk web
```

Then select "lead_qualification_agent" from the dropdown menu in the web UI.

## Example Interactions

Try these example interactions:

### Qualified Lead Example:
```
Lead Information:
Name: Sarah Johnson
Email: <EMAIL>
Phone: ************
Company: Tech Innovate Solutions
Position: CTO
Interest: Looking for an AI solution to automate customer support
Budget: $50K-100K available for the right solution
Timeline: Hoping to implement within next quarter
Notes: Currently using a competitor's product but unhappy with performance
```

### Unqualified Lead Example:
```
Lead Information:
Name: John Doe
Email: <EMAIL>
Interest: Something with AI maybe
Notes: Met at conference, seemed interested but was vague about needs
```

## How Sequential Agents Compare to Other Workflow Agents

ADK offers different types of workflow agents for different needs:

- **Sequential Agents**: For strict, ordered execution (like this example)
- **Loop Agents**: For repeated execution of sub-agents based on conditions
- **Parallel Agents**: For concurrent execution of independent sub-agents

## Additional Resources

- [ADK Sequential Agents Documentation](https://google.github.io/adk-docs/agents/workflow-agents/sequential-agents/)
- [Full Code Development Pipeline Example](https://google.github.io/adk-docs/agents/workflow-agents/sequential-agents/#full-example-code-development-pipeline)
````

## File: 11-parallel-agent/system_monitor_agent/subagents/cpu_info_agent/__init__.py
````python
from .agent import cpu_info_agent
````

## File: 11-parallel-agent/system_monitor_agent/subagents/cpu_info_agent/agent.py
````python
from google.adk.agents import LlmAgent

from .tools import get_cpu_info


GEMINI_MODEL = "gemini-2.0-flash"


cpu_info_agent = LlmAgent(
    name="CpuInfoAgent",
    model=GEMINI_MODEL,
    instruction="""You are a CPU Information Agent.

    When asked for system information, you should:
    1. Use the 'get_cpu_info' tool to gather CPU data
    2. Analyze the returned dictionary data
    3. Format this information into a concise, clear section of a system report

    The tool will return a dictionary with:
    - result: Core CPU information
    - stats: Key statistical data about CPU usage
    - additional_info: Context about the data collection

    Format your response as a well-structured report section with:
    - CPU core information (physical vs logical)
    - CPU usage statistics
    - Any performance concerns (high usage > 80%)

    IMPORTANT: You MUST call the get_cpu_info tool. Do not make up information.
    """,
    description="Gathers and analyzes CPU information",
    tools=[get_cpu_info],
    output_key="cpu_info",
)
````

## File: 11-parallel-agent/system_monitor_agent/subagents/cpu_info_agent/tools.py
````python
import time
from typing import Any, Dict

import psutil


def get_cpu_info() -> Dict[str, Any]:

    try:

        cpu_info = {
            "physical_cores": psutil.cpu_count(logical=False),
            "logical_cores": psutil.cpu_count(logical=True),
            "cpu_usage_per_core": [
                f"Core {i}: {percentage:.1f}%"
                for i, percentage in enumerate(
                    psutil.cpu_percent(interval=1, percpu=True)
                )
            ],
            "avg_cpu_usage": f"{psutil.cpu_percent(interval=1):.1f}%",
        }


        avg_usage = float(cpu_info["avg_cpu_usage"].strip("%"))
        high_usage = avg_usage > 80


        return {
            "result": cpu_info,
            "stats": {
                "physical_cores": cpu_info["physical_cores"],
                "logical_cores": cpu_info["logical_cores"],
                "avg_usage_percentage": avg_usage,
                "high_usage_alert": high_usage,
            },
            "additional_info": {
                "data_format": "dictionary",
                "collection_timestamp": time.time(),
                "performance_concern": (
                    "High CPU usage detected" if high_usage else None
                ),
            },
        }
    except Exception as e:
        return {
            "result": {"error": f"Failed to gather CPU information: {str(e)}"},
            "stats": {"success": False},
            "additional_info": {"error_type": str(type(e).__name__)},
        }
````

## File: 11-parallel-agent/system_monitor_agent/subagents/disk_info_agent/__init__.py
````python
from .agent import disk_info_agent
````

## File: 11-parallel-agent/system_monitor_agent/subagents/disk_info_agent/agent.py
````python
from google.adk.agents import LlmAgent

from .tools import get_disk_info


GEMINI_MODEL = "gemini-2.0-flash"


disk_info_agent = LlmAgent(
    name="DiskInfoAgent",
    model=GEMINI_MODEL,
    instruction="""You are a Disk Information Agent.

    When asked for system information, you should:
    1. Use the 'get_disk_info' tool to gather disk data
    2. Analyze the returned dictionary data
    3. Format this information into a concise, clear section of a system report

    The tool will return a dictionary with:
    - result: Core disk information including partitions
    - stats: Key statistical data about storage usage
    - additional_info: Context about the data collection

    Format your response as a well-structured report section with:
    - Partition information
    - Storage capacity and usage
    - Any storage concerns (high usage > 85%)

    IMPORTANT: You MUST call the get_disk_info tool. Do not make up information.
    """,
    description="Gathers and analyzes disk information",
    tools=[get_disk_info],
    output_key="disk_info",
)
````

## File: 11-parallel-agent/system_monitor_agent/subagents/disk_info_agent/tools.py
````python
import time
from typing import Any, Dict

import psutil


def get_disk_info() -> Dict[str, Any]:

    try:

        disk_info = {"partitions": []}
        partitions_over_threshold = []
        total_space = 0
        used_space = 0

        for partition in psutil.disk_partitions():
            try:
                partition_usage = psutil.disk_usage(partition.mountpoint)


                if partition_usage.percent > 85:
                    partitions_over_threshold.append(
                        f"{partition.mountpoint} ({partition_usage.percent:.1f}%)"
                    )


                total_space += partition_usage.total
                used_space += partition_usage.used

                disk_info["partitions"].append(
                    {
                        "device": partition.device,
                        "mountpoint": partition.mountpoint,
                        "filesystem_type": partition.fstype,
                        "total_size": f"{partition_usage.total / (1024 ** 3):.2f} GB",
                        "used": f"{partition_usage.used / (1024 ** 3):.2f} GB",
                        "free": f"{partition_usage.free / (1024 ** 3):.2f} GB",
                        "percentage": f"{partition_usage.percent:.1f}%",
                    }
                )
            except (PermissionError, FileNotFoundError):

                pass


        overall_usage_percent = (
            (used_space / total_space * 100) if total_space > 0 else 0
        )


        return {
            "result": disk_info,
            "stats": {
                "partition_count": len(disk_info["partitions"]),
                "total_space_gb": total_space / (1024**3),
                "used_space_gb": used_space / (1024**3),
                "overall_usage_percent": overall_usage_percent,
                "partitions_with_high_usage": len(partitions_over_threshold),
            },
            "additional_info": {
                "data_format": "dictionary",
                "collection_timestamp": time.time(),
                "high_usage_partitions": (
                    partitions_over_threshold if partitions_over_threshold else None
                ),
            },
        }
    except Exception as e:
        return {
            "result": {"error": f"Failed to gather disk information: {str(e)}"},
            "stats": {"success": False},
            "additional_info": {"error_type": str(type(e).__name__)},
        }
````

## File: 11-parallel-agent/system_monitor_agent/subagents/memory_info_agent/__init__.py
````python
from .agent import memory_info_agent
````

## File: 11-parallel-agent/system_monitor_agent/subagents/memory_info_agent/agent.py
````python
from google.adk.agents import LlmAgent

from .tools import get_memory_info


GEMINI_MODEL = "gemini-2.0-flash"


memory_info_agent = LlmAgent(
    name="MemoryInfoAgent",
    model=GEMINI_MODEL,
    instruction="""You are a Memory Information Agent.

    When asked for system information, you should:
    1. Use the 'get_memory_info' tool to gather memory data
    2. Analyze the returned dictionary data
    3. Format this information into a concise, clear section of a system report

    The tool will return a dictionary with:
    - result: Core memory information
    - stats: Key statistical data about memory usage
    - additional_info: Context about the data collection

    Format your response as a well-structured report section with:
    - Total and available memory
    - Memory usage statistics
    - Swap memory information
    - Any performance concerns (high usage > 80%)

    IMPORTANT: You MUST call the get_memory_info tool. Do not make up information.
    """,
    description="Gathers and analyzes memory information",
    tools=[get_memory_info],
    output_key="memory_info",
)
````

## File: 11-parallel-agent/system_monitor_agent/subagents/memory_info_agent/tools.py
````python
import time
from typing import Any, Dict

import psutil


def get_memory_info() -> Dict[str, Any]:

    try:

        memory = psutil.virtual_memory()
        swap = psutil.swap_memory()

        memory_info = {
            "total_memory": f"{memory.total / (1024 ** 3):.2f} GB",
            "available_memory": f"{memory.available / (1024 ** 3):.2f} GB",
            "used_memory": f"{memory.used / (1024 ** 3):.2f} GB",
            "memory_percentage": f"{memory.percent:.1f}%",
            "swap_total": f"{swap.total / (1024 ** 3):.2f} GB",
            "swap_used": f"{swap.used / (1024 ** 3):.2f} GB",
            "swap_percentage": f"{swap.percent:.1f}%",
        }


        memory_usage = memory.percent
        swap_usage = swap.percent
        high_memory_usage = memory_usage > 80
        high_swap_usage = swap_usage > 80


        return {
            "result": memory_info,
            "stats": {
                "memory_usage_percentage": memory_usage,
                "swap_usage_percentage": swap_usage,
                "total_memory_gb": memory.total / (1024**3),
                "available_memory_gb": memory.available / (1024**3),
            },
            "additional_info": {
                "data_format": "dictionary",
                "collection_timestamp": time.time(),
                "performance_concern": (
                    "High memory usage detected" if high_memory_usage else None
                ),
                "swap_concern": "High swap usage detected" if high_swap_usage else None,
            },
        }
    except Exception as e:
        return {
            "result": {"error": f"Failed to gather memory information: {str(e)}"},
            "stats": {"success": False},
            "additional_info": {"error_type": str(type(e).__name__)},
        }
````

## File: 11-parallel-agent/system_monitor_agent/subagents/synthesizer_agent/__init__.py
````python
from .agent import system_report_synthesizer
````

## File: 11-parallel-agent/system_monitor_agent/subagents/synthesizer_agent/agent.py
````python
from google.adk.agents import LlmAgent


GEMINI_MODEL = "gemini-2.0-flash"


system_report_synthesizer = LlmAgent(
    name="SystemReportSynthesizer",
    model=GEMINI_MODEL,
    instruction="""You are a System Report Synthesizer.

    Your task is to create a comprehensive system health report by combining information from:
    - CPU information: {cpu_info}
    - Memory information: {memory_info}
    - Disk information: {disk_info}

    Create a well-formatted report with:
    1. An executive summary at the top with overall system health status
    2. Sections for each component with their respective information
    3. Recommendations based on any concerning metrics

    Use markdown formatting to make the report readable and professional.
    Highlight any concerning values and provide practical recommendations.
    """,
    description="Synthesizes all system information into a comprehensive report",
)
````

## File: 11-parallel-agent/system_monitor_agent/subagents/__init__.py
````python
from . import cpu_info_agent, disk_info_agent, memory_info_agent, synthesizer_agent
````

## File: 11-parallel-agent/system_monitor_agent/__init__.py
````python
from .agent import root_agent
````

## File: 11-parallel-agent/system_monitor_agent/.env.example
````
GOOGLE_GENAI_USE_VERTEXAI=FALSE
GOOGLE_API_KEY=your_api_key_here
````

## File: 11-parallel-agent/system_monitor_agent/agent.py
````python
from google.adk.agents import ParallelAgent, SequentialAgent

from .subagents.cpu_info_agent import cpu_info_agent
from .subagents.disk_info_agent import disk_info_agent
from .subagents.memory_info_agent import memory_info_agent
from .subagents.synthesizer_agent import system_report_synthesizer


system_info_gatherer = ParallelAgent(
    name="system_info_gatherer",
    sub_agents=[cpu_info_agent, memory_info_agent, disk_info_agent],
)


root_agent = SequentialAgent(
    name="system_monitor_agent",
    sub_agents=[system_info_gatherer, system_report_synthesizer],
)
````

## File: 11-parallel-agent/README.md
````markdown
# Parallel Agents in ADK

This example demonstrates how to implement a Parallel Agent in the Agent Development Kit (ADK). The main agent in this example, `system_monitor_agent`, uses a Parallel Agent to gather system information concurrently and then synthesizes it into a comprehensive system health report.

## What are Parallel Agents?

Parallel Agents are workflow agents in ADK that:

1. **Execute Concurrently**: Sub-agents run simultaneously rather than sequentially
2. **Operate Independently**: Each sub-agent works independently without sharing state during execution
3. **Improve Performance**: Dramatically speed up workflows where tasks can be performed in parallel

Use Parallel Agents when you need to execute multiple independent tasks efficiently and time is a critical factor.

## System Monitoring Example

In this example, we've created a system monitoring application that uses a Parallel Agent to gather system information. The workflow consists of:

1. **Parallel System Information Gathering**: Using a `ParallelAgent` to concurrently collect data about:
   - CPU usage and statistics
   - Memory utilization
   - Disk space and usage

2. **Sequential Report Synthesis**: After parallel data collection, a synthesizer agent combines all information into a comprehensive report

### Sub-Agents

1. **CPU Info Agent**: Collects and analyzes CPU information
   - Retrieves core counts, usage statistics, and performance metrics
   - Identifies potential performance issues (high CPU usage)

2. **Memory Info Agent**: Gathers memory usage information
   - Collects total, used, and available memory
   - Analyzes memory pressure and swap usage

3. **Disk Info Agent**: Analyzes disk space and usage
   - Reports on total, used, and free disk space
   - Identifies disks that are running low on space

4. **System Report Synthesizer**: Combines all gathered information into a comprehensive system health report
   - Creates an executive summary of system health
   - Organizes component-specific information into sections
   - Provides recommendations based on system metrics

### How It Works

The architecture combines both parallel and sequential workflow patterns:

1. First, the `system_info_gatherer` Parallel Agent runs all three information agents concurrently
2. Then, the `system_report_synthesizer` uses the collected data to generate a final report

This hybrid approach demonstrates how to combine workflow agent types for optimal performance and logical flow.

## Project Structure

```
10-parallel-agent/
│
├── system_monitor_agent/          # Main System Monitor Agent package
│   ├── __init__.py                # Package initialization
│   ├── agent.py                   # Agent definitions (root_agent)
│   │
│   └── subagents/                 # Sub-agents folder
│       ├── __init__.py            # Sub-agents initialization
│       │
│       ├── cpu_info_agent/        # CPU information agent
│       │   ├── __init__.py
│       │   ├── agent.py
│       │   └── tools.py           # CPU info collection tools
│       │
│       ├── memory_info_agent/     # Memory information agent
│       │   ├── __init__.py
│       │   ├── agent.py
│       │   └── tools.py           # Memory info collection tools
│       │
│       ├── disk_info_agent/       # Disk information agent
│       │   ├── __init__.py
│       │   ├── agent.py
│       │   └── tools.py           # Disk info collection tools
│       │
│       └── synthesizer_agent/     # Report synthesizing agent
│           ├── __init__.py
│           └── agent.py
│
├── .env.example                   # Environment variables example
└── README.md                      # This documentation
```

## Getting Started

### Setup

1. Activate the virtual environment from the root directory:
```bash
# macOS/Linux:
source ../.venv/bin/activate
# Windows CMD:
..\.venv\Scripts\activate.bat
# Windows PowerShell:
..\.venv\Scripts\Activate.ps1
```

2. Copy the `.env.example` file to `.env` and add your Google API key:
```
GOOGLE_API_KEY=your_api_key_here
```

### Running the Example

```bash
cd 10-parallel-agent
adk web
```

Then select "system_monitor_agent" from the dropdown menu in the web UI.

## Example Interactions

Try these example prompts:

```
Check my system health
```

```
Provide a comprehensive system report with recommendations
```

```
Is my system running out of memory or disk space?
```

## Key Concepts: Independent Execution

One key aspect of Parallel Agents is that **sub-agents run independently without sharing state during execution**. In this example:

1. Each information gathering agent operates in isolation
2. The results from each agent are collected after parallel execution completes
3. The synthesizer agent then uses these collected results to create the final report

This approach is ideal for scenarios where tasks are completely independent and don't require interaction during execution.

## How Parallel Agents Compare to Other Workflow Agents

ADK offers different types of workflow agents for different needs:

- **Sequential Agents**: For strict, ordered execution where each step depends on previous outputs
- **Loop Agents**: For repeated execution of sub-agents based on conditions
- **Parallel Agents**: For concurrent execution of independent sub-agents (like this example)

## Additional Resources

- [ADK Parallel Agents Documentation](https://google.github.io/adk-docs/agents/workflow-agents/parallel-agents/)
- [Full Example: Parallel Web Research](https://google.github.io/adk-docs/agents/workflow-agents/parallel-agents/#full-example-parallel-web-research)
````

## File: 12-loop-agent/linkedin_post_agent/subagents/post_generator/__init__.py
````python
from .agent import initial_post_generator
````

## File: 12-loop-agent/linkedin_post_agent/subagents/post_generator/agent.py
````python
from google.adk.agents.llm_agent import LlmAgent


GEMINI_MODEL = "gemini-2.0-flash"


initial_post_generator = LlmAgent(
    name="InitialPostGenerator",
    model=GEMINI_MODEL,
    instruction="""You are a LinkedIn Post Generator.

    Your task is to create a LinkedIn post about an Agent Development Kit (ADK) tutorial by @aiwithbrandon.

    ## CONTENT REQUIREMENTS
    Ensure the post includes:
    1. Excitement about learning from the tutorial
    2. Specific aspects of ADK learned:
       - Basic agent implementation (basic-agent)
       - Tool integration (tool-agent)
       - Using LiteLLM (litellm-agent)
       - Managing sessions and memory
       - Persistent storage capabilities
       - Multi-agent orchestration
       - Stateful multi-agent systems
       - Callback systems
       - Sequential agents for pipeline workflows
       - Parallel agents for concurrent operations
       - Loop agents for iterative refinement
    3. Brief statement about improving AI applications
    4. Mention/tag of @aiwithbrandon
    5. Clear call-to-action for connections

    ## STYLE REQUIREMENTS
    - Professional and conversational tone
    - Between 1000-1500 characters
    - NO emojis
    - NO hashtags
    - Show genuine enthusiasm
    - Highlight practical applications

    ## OUTPUT INSTRUCTIONS
    - Return ONLY the post content
    - Do not add formatting markers or explanations
    """,
    description="Generates the initial LinkedIn post to start the refinement process",
    output_key="current_post",
)
````

## File: 12-loop-agent/linkedin_post_agent/subagents/post_refiner/__init__.py
````python
from .agent import post_refiner
````

## File: 12-loop-agent/linkedin_post_agent/subagents/post_refiner/agent.py
````python
from google.adk.agents.llm_agent import LlmAgent


GEMINI_MODEL = "gemini-2.0-flash"


post_refiner = LlmAgent(
    name="PostRefinerAgent",
    model=GEMINI_MODEL,
    instruction="""You are a LinkedIn Post Refiner.

    Your task is to refine a LinkedIn post based on review feedback.

    ## INPUTS
    **Current Post:**
    {current_post}

    **Review Feedback:**
    {review_feedback}

    ## TASK
    Carefully apply the feedback to improve the post.
    - Maintain the original tone and theme of the post
    - Ensure all content requirements are met:
      1. Excitement about learning from the tutorial
      2. Specific aspects of ADK learned (at least 4)
      3. Brief statement about improving AI applications
      4. Mention/tag of @aiwithbrandon
      5. Clear call-to-action for connections
    - Adhere to style requirements:
      - Professional and conversational tone
      - Between 1000-1500 characters
      - NO emojis
      - NO hashtags
      - Show genuine enthusiasm
      - Highlight practical applications

    ## OUTPUT INSTRUCTIONS
    - Output ONLY the refined post content
    - Do not add explanations or justifications
    """,
    description="Refines LinkedIn posts based on feedback to improve quality",
    output_key="current_post",
)
````

## File: 12-loop-agent/linkedin_post_agent/subagents/post_reviewer/__init__.py
````python
from .agent import post_reviewer
````

## File: 12-loop-agent/linkedin_post_agent/subagents/post_reviewer/agent.py
````python
from google.adk.agents.llm_agent import LlmAgent

from .tools import count_characters, exit_loop


GEMINI_MODEL = "gemini-2.0-flash"


post_reviewer = LlmAgent(
    name="PostReviewer",
    model=GEMINI_MODEL,
    instruction="""You are a LinkedIn Post Quality Reviewer.

    Your task is to evaluate the quality of a LinkedIn post about Agent Development Kit (ADK).

    ## EVALUATION PROCESS
    1. Use the count_characters tool to check the post's length.
       Pass the post text directly to the tool.

    2. If the length check fails (tool result is "fail"), provide specific feedback on what needs to be fixed.
       Use the tool's message as a guideline, but add your own professional critique.

    3. If length check passes, evaluate the post against these criteria:
       - REQUIRED ELEMENTS:
         1. Mentions @aiwithbrandon
         2. Lists multiple ADK capabilities (at least 4)
         3. Has a clear call-to-action
         4. Includes practical applications
         5. Shows genuine enthusiasm

       - STYLE REQUIREMENTS:
         1. NO emojis
         2. NO hashtags
         3. Professional tone
         4. Conversational style
         5. Clear and concise writing

    ## OUTPUT INSTRUCTIONS
    IF the post fails ANY of the checks above:
      - Return concise, specific feedback on what to improve

    ELSE IF the post meets ALL requirements:
      - Call the exit_loop function
      - Return "Post meets all requirements. Exiting the refinement loop."

    Do not embellish your response. Either provide feedback on what to improve OR call exit_loop and return the completion message.

    ## POST TO REVIEW
    {current_post}
    """,
    description="Reviews post quality and provides feedback on what to improve or exits the loop if requirements are met",
    tools=[count_characters, exit_loop],
    output_key="review_feedback",
)
````

## File: 12-loop-agent/linkedin_post_agent/subagents/post_reviewer/tools.py
````python
from typing import Any, Dict

from google.adk.tools.tool_context import ToolContext


def count_characters(text: str, tool_context: ToolContext) -> Dict[str, Any]:

    char_count = len(text)
    MIN_LENGTH = 1000
    MAX_LENGTH = 1500

    print("\n----------- TOOL DEBUG -----------")
    print(f"Checking text length: {char_count} characters")
    print("----------------------------------\n")

    if char_count < MIN_LENGTH:
        chars_needed = MIN_LENGTH - char_count
        tool_context.state["review_status"] = "fail"
        return {
            "result": "fail",
            "char_count": char_count,
            "chars_needed": chars_needed,
            "message": f"Post is too short. Add {chars_needed} more characters to reach minimum length of {MIN_LENGTH}.",
        }
    elif char_count > MAX_LENGTH:
        chars_to_remove = char_count - MAX_LENGTH
        tool_context.state["review_status"] = "fail"
        return {
            "result": "fail",
            "char_count": char_count,
            "chars_to_remove": chars_to_remove,
            "message": f"Post is too long. Remove {chars_to_remove} characters to meet maximum length of {MAX_LENGTH}.",
        }
    else:
        tool_context.state["review_status"] = "pass"
        return {
            "result": "pass",
            "char_count": char_count,
            "message": f"Post length is good ({char_count} characters).",
        }


def exit_loop(tool_context: ToolContext) -> Dict[str, Any]:

    print("\n----------- EXIT LOOP TRIGGERED -----------")
    print("Post review completed successfully")
    print("Loop will exit now")
    print("------------------------------------------\n")

    tool_context.actions.escalate = True
    return {}
````

## File: 12-loop-agent/linkedin_post_agent/subagents/__init__.py
````python
from .post_generator import initial_post_generator
from .post_refiner import post_refiner
from .post_reviewer import post_reviewer
````

## File: 12-loop-agent/linkedin_post_agent/__init__.py
````python
from .agent import root_agent
````

## File: 12-loop-agent/linkedin_post_agent/agent.py
````python
from google.adk.agents import LoopAgent, SequentialAgent

from .subagents.post_generator import initial_post_generator
from .subagents.post_refiner import post_refiner
from .subagents.post_reviewer import post_reviewer


refinement_loop = LoopAgent(
    name="PostRefinementLoop",
    max_iterations=10,
    sub_agents=[
        post_reviewer,
        post_refiner,
    ],
    description="Iteratively reviews and refines a LinkedIn post until quality requirements are met",
)


root_agent = SequentialAgent(
    name="LinkedInPostGenerationPipeline",
    sub_agents=[
        initial_post_generator,
        refinement_loop,
    ],
    description="Generates and refines a LinkedIn post through an iterative review process",
)
````

## File: 12-loop-agent/README.md
````markdown
# LinkedIn Post Generator Loop Agent

This example demonstrates the use of a Sequential and Loop Agent pattern in the Agent Development Kit (ADK) to generate and refine a LinkedIn post.

## Overview

The LinkedIn Post Generator uses a sequential pipeline with a loop component to:
1. Generate an initial LinkedIn post
2. Iteratively refine the post until quality requirements are met

This demonstrates several key patterns:
1. **Sequential Pipeline**: A multi-step workflow with distinct stages
2. **Iterative Refinement**: Using a loop to repeatedly refine content
3. **Automatic Quality Checking**: Validating content against specific criteria
4. **Feedback-Driven Refinement**: Improving content based on specific feedback
5. **Loop Exit Tool**: Using a tool to terminate the loop when quality requirements are met

## Architecture

The system is composed of the following components:

### Root Sequential Agent

`LinkedInPostGenerationPipeline` - A SequentialAgent that orchestrates the overall process:
1. First runs the initial post generator
2. Then executes the refinement loop

### Initial Post Generator

`InitialPostGenerator` - An LlmAgent that creates the first draft of the LinkedIn post with no prior context.

### Refinement Loop

`PostRefinementLoop` - A LoopAgent that executes a two-stage refinement process:
1. First runs the reviewer to evaluate the post and possibly exit the loop
2. Then runs the refiner to improve the post if the loop continues

### Sub-Agents Inside the Refinement Loop

1. **Post Reviewer** (`PostReviewer`) - Reviews posts for quality and provides feedback or exits the loop if requirements are met
2. **Post Refiner** (`PostRefiner`) - Refines the post based on feedback to improve quality

### Tools

1. **Character Counter** - Validates post length against requirements (used by the Reviewer)
2. **Exit Loop** - Terminates the loop when all quality criteria are satisfied (used by the Reviewer)

## Loop Control with Exit Tool

A key design pattern in this example is the use of an `exit_loop` tool to control when the loop terminates. The Post Reviewer has two responsibilities:

1. **Quality Evaluation**: Checks if the post meets all requirements
2. **Loop Control**: Calls the exit_loop tool when the post passes all quality checks

When the exit_loop tool is called:
1. It sets `tool_context.actions.escalate = True`
2. This signals to the LoopAgent that it should stop iterating

This approach follows ADK best practices by:
1. Separating initial generation from refinement
2. Giving the quality reviewer direct control over loop termination
3. Using a dedicated agent for post refinement
4. Using a tool to manage the loop control flow

## Usage

To run this example:

```bash
cd 11-loop-agent
adk web
```

Then in the web interface, enter a prompt like:
"Generate a LinkedIn post about what I've learned from @aiwithbrandon's Agent Development Kit tutorial."

The system will:
1. Generate an initial LinkedIn post
2. Review the post for quality and compliance with requirements
3. If the post meets all requirements, exit the loop
4. Otherwise, provide feedback and refine the post
5. Continue this process until a satisfactory post is created or max iterations reached
6. Return the final post

## Example Input

```
Generate a LinkedIn post about what I've learned from @aiwithbrandon's Agent Development Kit tutorial.
```

## Loop Termination

The loop terminates in one of two ways:
1. When the post meets all quality requirements (reviewer calls the exit_loop tool)
2. After reaching the maximum number of iterations (10)
````

## File: 2-tool-agent/tool_agent/__init__.py
````python
from . import agent
````

## File: 2-tool-agent/tool_agent/.env.example
````
GOOGLE_GENAI_USE_VERTEXAI=FALSE
GOOGLE_API_KEY=...
````

## File: 2-tool-agent/tool_agent/agent.py
````python
from google.adk.agents import Agent
from google.adk.tools import google_search



#     Get the current time in the format YYYY-MM-DD HH:MM:SS
#     """




root_agent = Agent(
    name="tool_agent",
    model="gemini-2.0-flash",
    description="Tool agent",
    instruction="""
    You are a helpful assistant that can use the following tools:
    - google_search
    """,
    tools=[google_search],


)
````

## File: 2-tool-agent/README.md
````markdown
# Tool Agent Example

## What is a Tool Agent?

A Tool Agent extends the basic ADK agent by incorporating tools that allow the agent to perform actions beyond just generating text responses. Tools enable agents to interact with external systems, retrieve information, and perform specific functions to accomplish tasks more effectively.

In this example, we demonstrate how to build an agent that can use built-in tools (like Google Search) and custom function tools to enhance its capabilities.

## Key Components

### 1. Built-in Tools
ADK provides several built-in tools that you can use with your agents:

- **Google Search**: Allows your agent to search the web for information
- **Code Execution**: Enables your agent to run code snippets
- **Vertex AI Search**: Lets your agent search through your own data

**Important Note**: Currently, for each root agent or single agent, only one built-in tool is supported. See the [ADK documentation](https://google.github.io/adk-docs/tools/built-in-tools/#use-built-in-tools-with-other-tools) for more details.

### 2. Custom Function Tools
You can create your own tools by defining Python functions. These custom tools extend your agent's capabilities to perform specific tasks.

#### Best Practices for Custom Function Tools:

- **Parameters**: Define your function parameters using standard JSON-serializable types (string, integer, list, dictionary)
- **No Default Values**: Default values are not currently supported in ADK
- **Return Type**: The preferred return type is a dictionary
  - If you don't return a dictionary, ADK will wrap it into a dictionary `{"result": ...}`
  - Best practice format: `{"status": "success", "error_message": None, "result": "..."}`
- **Docstrings**: The function's docstring serves as the tool's description and is sent to the LLM
  - Focus on clarity so the LLM understands how to use the tool effectively

## Limitations

When working with built-in tools in ADK, there are several important limitations to be aware of:

### Single Built-in Tool Restriction

**Currently, for each root agent or single agent, only one built-in tool is supported.**

For example, this approach using two built-in tools within a single agent is **not** currently supported:

```python
root_agent = Agent(
    name="RootAgent",
    model="gemini-2.0-flash",
    description="Root Agent",
    tools=[built_in_code_execution, google_search],  # NOT SUPPORTED
)
```

### Built-in Tools vs. Custom Tools

**You cannot mix built-in tools with custom function tools in the same agent.**

For example, this approach is **not** currently supported:

```python
def get_current_time() -> dict:
    """Get the current time in the format YYYY-MM-DD HH:MM:SS"""
    return {
        "current_time": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
    }

root_agent = Agent(
    name="RootAgent",
    model="gemini-2.0-flash",
    description="Root Agent",
    tools=[google_search, get_current_time],  # NOT SUPPORTED
)
```

To use both types of tools, you would need to use the Agent Tool approach described in the Multi-Agent example.

## Implementation Example

### Understanding the Code

The agent.py file defines a tool agent that can use Google Search to find information on the web. The agent is configured with:

1. A name and description
2. The Gemini model to use
3. Instructions that tell the agent how to behave and what tools it can use
4. The tools it can access (in this case, google_search)

The file also includes a commented-out example of a custom function tool `get_current_time()` that could be uncommented to explore custom tool functionality.

### Getting Started

This example uses the same virtual environment created in the root directory. Make sure you have:

1. Activated the virtual environment from the root directory:
```bash
# macOS/Linux:
source ../.venv/bin/activate
# Windows CMD:
..\.venv\Scripts\activate.bat
# Windows PowerShell:
..\.venv\Scripts\Activate.ps1
```

2. Set up your API key:
   - Rename `.env.example` to `.env` in the tool_agent folder
   - Add your Google API key to the `GOOGLE_API_KEY` variable in the `.env` file

### Running the Example

To run the tool agent example:

1. Navigate to the 2-tool-agent directory containing your agent folder.

2. Start the interactive web UI:
```bash
adk web
```

3. Access the web UI by opening the URL shown in your terminal (typically http://localhost:8000)

4. Select the "tool_agent" from the dropdown menu in the top-left corner of the UI

5. Start chatting with your agent in the textbox at the bottom of the screen

The ADK CLI tool provides several options:

- **`adk web`**: Launches an interactive web UI for testing your agent with a chat interface
- **`adk run tool_agent`**: Runs your agent directly in the terminal
- **`adk api_server`**: Starts a FastAPI server to test API requests to your agent

### Example Prompts to Try

- "Search for recent news about artificial intelligence"
- "Find information about Google's Agent Development Kit"
- "What are the latest advancements in quantum computing?"

You can exit the conversation or stop the server by pressing `Ctrl+C` in your terminal.

## Additional Resources

- [Types of tools](https://google.github.io/adk-docs/tools/#full-example-tavily-search)
- [ADK Function Tools Documentation](https://google.github.io/adk-docs/tools/function-tools/)
- [ADK Built-in Tools Documentation](https://google.github.io/adk-docs/tools/built-in-tools/)
````

## File: 3-litellm-agent/dad_joke_agent/__init__.py
````python
from . import agent
````

## File: 3-litellm-agent/dad_joke_agent/.env.example
````
OPENROUTER_API_KEY=...
````

## File: 3-litellm-agent/dad_joke_agent/agent.py
````python
import os
import random

from google.adk.agents import Agent
from google.adk.models.lite_llm import LiteLlm


model = LiteLlm(
    model="openrouter/openai/gpt-4.1",
    api_key=os.getenv("OPENROUTER_API_KEY"),
)


def get_dad_joke():
    jokes = [
        "Why did the chicken cross the road? To get to the other side!",
        "What do you call a belt made of watches? A waist of time.",
        "What do you call fake spaghetti? An impasta!",
        "Why did the scarecrow win an award? Because he was outstanding in his field!",
    ]
    return random.choice(jokes)


root_agent = Agent(
    name="dad_joke_agent",
    model=model,
    description="Dad joke agent",
    instruction="""
    You are a helpful assistant that can tell dad jokes.
    Only use the tool `get_dad_joke` to tell jokes.
    """,
    tools=[get_dad_joke],
)
````

## File: 3-litellm-agent/README.md
````markdown
# LiteLLM Agent Example

## What is LiteLLM?

LiteLLM is a Python library that provides a unified interface for interacting with multiple Large Language Model (LLM) providers through a single, consistent API. It serves as an adapter that allows you to:

- Use the same code to access 100+ different LLMs from providers like OpenAI, Anthropic, Google, AWS Bedrock, and more
- Standardize inputs and outputs across different LLM providers
- Track costs, manage API keys, and handle errors consistently
- Implement fallbacks and load balancing across different models

In essence, LiteLLM acts as a unified wrapper that makes it easy to switch between different LLM providers without changing your application code.

## Why Use LiteLLM with ADK?

The Agent Development Kit (ADK) is designed to be model-agnostic, meaning it can work with various LLM providers. LiteLLM enhances this capability by:

1. **Provider Flexibility**: Easily switch between LLM providers (OpenAI, Anthropic, etc.) without changing your agent code
2. **Cost Optimization**: Choose the most cost-effective model for your specific use case
3. **Model Exploration**: Experiment with different models to find the best performance for your task
4. **Future-Proofing**: As new models are released, you can quickly adopt them without major code changes

This example demonstrates how to use LiteLLM with ADK to create an agent powered by models through OpenRouter rather than Google's Gemini models.

## Limitations When Using Non-Google Models

When using LiteLLM to integrate non-Google models with ADK, there are some important limitations to be aware of:

1. **No Access to Google Built-in Tools**: Non-Google models (like OpenAI, Anthropic, etc.) cannot use ADK's built-in Google tools such as:
   - Google Search
   - Code Execution
   - Vertex AI Search

2. **Custom Function Tools Only**: When using non-Google models, you can only use custom function tools (like the `get_dad_joke()` function in this example).


These limitations exist because built-in tools are specifically designed to work with Google's models and infrastructure. However, you can still create powerful agents using custom function tools and the wide variety of models available through LiteLLM.

## Getting Started

This example uses the same virtual environment created in the root directory. Make sure you have:

1. Activated the virtual environment from the root directory:
```bash
# macOS/Linux:
source ../.venv/bin/activate
# Windows CMD:
..\.venv\Scripts\activate.bat
# Windows PowerShell:
..\.venv\Scripts\Activate.ps1
```

2. Set up your OpenRouter API key:
   - Create an account at [OpenRouter](https://openrouter.ai/) if you don't have one
   - Generate an API key at https://openrouter.ai/keys
   - Rename `.env.example` to `.env` in the openrouter_dad_joke_agent folder
   - Add your OpenRouter API key to the `OPENROUTER_API_KEY` variable in the `.env` file

## Understanding the Code

This example demonstrates:

1. How to use the `LiteLlm` model adapter with ADK
2. How to connect to models through OpenRouter (specifically Claude 3.5 Sonnet)
3. How to create a simple agent with a custom tool

The agent is configured to tell dad jokes using a custom function tool `get_dad_joke()` and powered by Anthropic's Claude 3.5 Sonnet model through OpenRouter instead of Google's Gemini.

## Running the Example

To run the LiteLLM agent example:

1. Navigate to the 3-litellm-agent directory containing your agent folder.

2. Start the interactive web UI:
```bash
adk web
```

3. Access the web UI by opening the URL shown in your terminal (typically http://localhost:8000)

4. Select the "openrouter_dad_joke_agent" from the dropdown menu in the top-left corner of the UI

5. Start chatting with your agent in the textbox at the bottom of the screen

### Example Prompts to Try

- "Tell me a dad joke"

You can exit the conversation or stop the server by pressing `Ctrl+C` in your terminal.

## Modifying the Example

You can easily modify this example to use different models from different providers through OpenRouter by changing the `LiteLlm` configuration. For example:

```python
# To use Claude 3.5 Sonnet from Anthropic through OpenRouter
model = LiteLlm(
    model="openrouter/anthropic/claude-3-5-sonnet",
    api_key=os.getenv("OPENROUTER_API_KEY"),
)

# To use GPT-4o from OpenAI through OpenRouter
model = LiteLlm(
    model="openrouter/openai/gpt-4o",
    api_key=os.getenv("OPENROUTER_API_KEY"),
)

# To use Llama 3 70B from Meta through OpenRouter
model = LiteLlm(
    model="openrouter/meta-llama/meta-llama-3-70b-instruct",
    api_key=os.getenv("OPENROUTER_API_KEY"),
)

# To use Mistral Large through OpenRouter
model = LiteLlm(
    model="openrouter/mistral/mistral-large-latest",
    api_key=os.getenv("OPENROUTER_API_KEY"),
)
```

## Additional Resources

- [Google ADK LiteLLM Integration Documentation](https://google.github.io/adk-docs/tutorials/agent-team/#step-2-going-multi-model-with-litellm-optional)
- [LiteLLM Documentation](https://docs.litellm.ai/docs/)
- [LiteLLM Supported Providers](https://docs.litellm.ai/docs/providers)
- [OpenRouter Documentation](https://openrouter.ai/docs)
- [Anthropic Claude Models Overview](https://docs.anthropic.com/en/docs/about-claude/models/all-models)
````

## File: 4-structured-outputs/email_agent/__init__.py
````python
from . import agent
````

## File: 4-structured-outputs/email_agent/.env.example
````
GOOGLE_GENAI_USE_VERTEXAI=FALSE
GOOGLE_API_KEY=your_api_key_here
````

## File: 4-structured-outputs/email_agent/agent.py
````python
from google.adk.agents import LlmAgent
from pydantic import BaseModel, Field



class EmailContent(BaseModel):
    subject: str = Field(
        description="The subject line of the email. Should be concise and descriptive."
    )
    body: str = Field(
        description="The main content of the email. Should be well-formatted with proper greeting, paragraphs, and signature."
    )



root_agent = LlmAgent(
    name="email_agent",
    model="gemini-2.0-flash",
    instruction="""
        You are an Email Generation Assistant.
        Your task is to generate a professional email based on the user's request.

        GUIDELINES:
        - Create an appropriate subject line (concise and relevant)
        - Write a well-structured email body with:
            * Professional greeting
            * Clear and concise main content
            * Appropriate closing
            * Your name as signature
        - Suggest relevant attachments if applicable (empty list if none needed)
        - Email tone should match the purpose (formal for business, friendly for colleagues)
        - Keep emails concise but complete

        IMPORTANT: Your response MUST be valid JSON matching this structure:
        {
            "subject": "Subject line here",
            "body": "Email body here with proper paragraphs and formatting",
        }

        DO NOT include any explanations or additional text outside the JSON response.
    """,
    description="Generates professional emails with structured subject and body",
    output_schema=EmailContent,
    output_key="email",
)
````

## File: 4-structured-outputs/README.md
````markdown
# Structured Outputs in ADK

This example demonstrates how to implement structured outputs in the Agent Development Kit (ADK) using Pydantic models. The main agent in this example, `email_generator`, uses the `output_schema` parameter to ensure its responses conform to a specific structured format.

## What are Structured Outputs?

ADK allows you to define structured data formats for agent inputs and outputs using Pydantic models:

1. **Controlled Output Format**: Using `output_schema` ensures the LLM produces responses in a consistent JSON structure
2. **Data Validation**: Pydantic validates that all required fields are present and correctly formatted
3. **Improved Downstream Processing**: Structured outputs are easier to handle in downstream applications or by other agents

Use structured outputs when you need guaranteed format consistency for integration with other systems or agents.

## Email Generator Example

In this example, we've created an email generator agent that produces structured output with:

1. **Email Subject**: A concise, relevant subject line
2. **Email Body**: Well-formatted email content with greeting, paragraphs, and signature

The agent uses a Pydantic model called `EmailContent` to define this structure, ensuring every response follows the same format.

### Output Schema Definition

The Pydantic model defines exactly what fields are required and includes descriptions for each:

```python
class EmailContent(BaseModel):
    """Schema for email content with subject and body."""
    
    subject: str = Field(
        description="The subject line of the email. Should be concise and descriptive."
    )
    body: str = Field(
        description="The main content of the email. Should be well-formatted with proper greeting, paragraphs, and signature."
    )
```

### How It Works

1. The user provides a description of the email they need
2. The LLM agent processes this request and generates both a subject and body
3. The agent formats its response as a JSON object matching the `EmailContent` schema
4. ADK validates the response against the schema before returning it
5. The structured output is stored in the session state under the specified `output_key`

## Important Limitations

When using `output_schema`:

1. **No Tool Usage**: Agents with an output schema cannot use tools during their execution
2. **Direct JSON Response**: The LLM must produce a JSON response matching the schema as its final output
3. **Clear Instructions**: The agent's instructions must explicitly guide the LLM to produce properly formatted JSON

## Project Structure

```
4-structured-outputs/
│
├── email_agent/                   # Email Generator Agent package
│   └── agent.py                   # Agent definition with output schema
│
└── README.md                      # This documentation
```

## Getting Started

### Setup

1. Activate the virtual environment from the root directory:
```bash
# macOS/Linux:
source ../.venv/bin/activate
# Windows CMD:
..\.venv\Scripts\activate.bat
# Windows PowerShell:
..\.venv\Scripts\Activate.ps1
```

2. Create a `.env` file and add your Google API key:
```
GOOGLE_API_KEY=your_api_key_here
```

### Running the Example

```bash
cd 4-structured-outputs
adk web
```

Then select "email_generator" from the dropdown menu in the web UI.

## Example Interactions

Try these example prompts:

```
Write a professional email to my team about the upcoming project deadline that has been extended by two weeks.
```

```
Draft an email to a client explaining that we need additional information before we can proceed with their order.
```

```
Create an email to schedule a meeting with the marketing department to discuss the new product launch strategy.
```

## Key Concepts: Structured Data Exchange

Structured outputs are part of ADK's broader support for structured data exchange, which includes:

1. **input_schema**: Define expected input format (not used in this example)
2. **output_schema**: Define required output format (used in this example)
3. **output_key**: Store the result in session state for use by other agents (used in this example)

This pattern enables reliable data passing between agents and integration with external systems that expect consistent data formats.

## Additional Resources

- [ADK Structured Data Documentation](https://google.github.io/adk-docs/agents/llm-agents/#structuring-data-input_schema-output_schema-output_key)
- [Pydantic Documentation](https://docs.pydantic.dev/latest/)
````

## File: 5-sessions-and-state/question_answering_agent/__init__.py
````python
from .agent import question_answering_agent
````

## File: 5-sessions-and-state/question_answering_agent/.env.example
````
GOOGLE_GENAI_USE_VERTEXAI=FALSE
GOOGLE_API_KEY=...
````

## File: 5-sessions-and-state/question_answering_agent/agent.py
````python
from google.adk.agents import Agent


question_answering_agent = Agent(
    name="question_answering_agent",
    model="gemini-2.0-flash",
    description="Question answering agent",
    instruction="""
    You are a helpful assistant that answers questions about the user's preferences.

    Here is some information about the user:
    Name:
    {user_name}
    Preferences:
    {user_preferences}
    """,
)
````

## File: 5-sessions-and-state/basic_stateful_session.py
````python
import uuid

from dotenv import load_dotenv
from google.adk.runners import Runner
from google.adk.sessions import InMemorySessionService
from google.genai import types
from question_answering_agent import question_answering_agent

load_dotenv()



session_service_stateful = InMemorySessionService()

initial_state = {
    "user_name": "Brandon Hancock",
    "user_preferences": """
        I like to play Pickleball, Disc Golf, and Tennis.
        My favorite food is Mexican.
        My favorite TV show is Game of Thrones.
        Loves it when people like and subscribe to his YouTube channel.
    """,
}


APP_NAME = "Brandon Bot"
USER_ID = "brandon_hancock"
SESSION_ID = str(uuid.uuid4())
stateful_session = session_service_stateful.create_session(
    app_name=APP_NAME,
    user_id=USER_ID,
    session_id=SESSION_ID,
    state=initial_state,
)
print("CREATED NEW SESSION:")
print(f"\tSession ID: {SESSION_ID}")

runner = Runner(
    agent=question_answering_agent,
    app_name=APP_NAME,
    session_service=session_service_stateful,
)

new_message = types.Content(
    role="user", parts=[types.Part(text="What is Brandon's favorite TV show?")]
)

for event in runner.run(
    user_id=USER_ID,
    session_id=SESSION_ID,
    new_message=new_message,
):
    if event.is_final_response():
        if event.content and event.content.parts:
            print(f"Final Response: {event.content.parts[0].text}")

print("==== Session Event Exploration ====")
session = session_service_stateful.get_session(
    app_name=APP_NAME, user_id=USER_ID, session_id=SESSION_ID
)


print("=== Final Session State ===")
for key, value in session.state.items():
    print(f"{key}: {value}")
````

## File: 5-sessions-and-state/README.md
````markdown
# Sessions and State Management in ADK

This example demonstrates how to create and manage stateful sessions in the Agent Development Kit (ADK), enabling your agents to maintain context and remember user information across interactions.

## What Are Sessions in ADK?

Sessions in ADK provide a way to:

1. **Maintain State**: Store and access user data, preferences, and other information between interactions
2. **Track Conversation History**: Automatically record and retrieve message history
3. **Personalize Responses**: Use stored information to create more contextual and personalized agent experiences

Unlike simple conversational agents that forget previous interactions, stateful agents can build relationships with users over time by remembering important details and preferences.

## Example Overview

This directory contains a basic stateful session example that demonstrates:

- Creating a session with user preferences
- Using template variables to access session state in agent instructions
- Running the agent with a session to maintain context

The example uses a simple question-answering agent that responds based on stored user information in the session state.

## Project Structure

```
5-sessions-and-state/
│
├── basic_stateful_session.py      # Main example script
│
└── question_answering_agent/      # Agent implementation
    ├── __init__.py
    └── agent.py                   # Agent definition with template variables
```

## Getting Started

### Setup

1. Activate the virtual environment from the root directory:
```bash
# macOS/Linux:
source ../.venv/bin/activate
# Windows CMD:
..\.venv\Scripts\activate.bat
# Windows PowerShell:
..\.venv\Scripts\Activate.ps1
```

2. Create a `.env` file and add your Google API key:
```
GOOGLE_API_KEY=your_api_key_here
```

### Running the Example

Run the example to see a stateful session in action:

```bash
python basic_stateful_session.py
```

This will:
1. Create a new session with user information
2. Initialize the agent with access to that session
3. Process a user query about the stored preferences
4. Display the agent's response based on the session data

## Key Components

### Session Service

The example uses the `InMemorySessionService` which stores sessions in memory:

```python
session_service = InMemorySessionService()
```

### Initial State

Sessions are created with an initial state containing user information:

```python
initial_state = {
    "user_name": "Brandon Hancock",
    "user_preferences": """
        I like to play Pickleball, Disc Golf, and Tennis.
        My favorite food is Mexican.
        My favorite TV show is Game of Thrones.
        Loves it when people like and subscribe to his YouTube channel.
    """,
}
```

### Creating a Session

The example creates a session with a unique identifier:

```python
stateful_session = session_service.create_session(
    app_name=APP_NAME,
    user_id=USER_ID,
    session_id=SESSION_ID,
    state=initial_state,
)
```

### Accessing State in Agent Instructions

The agent accesses session state using template variables in its instructions:

```python
instruction="""
You are a helpful assistant that answers questions about the user's preferences.

Here is some information about the user:
Name: 
{user_name}
Preferences: 
{user_preferences}
"""
```

### Running with Sessions

Sessions are integrated with the `Runner` to maintain state between interactions:

```python
runner = Runner(
    agent=question_answering_agent,
    app_name=APP_NAME,
    session_service=session_service,
)
```

## Additional Resources

- [Google ADK Sessions Documentation](https://google.github.io/adk-docs/sessions/session/)
- [State Management in ADK](https://google.github.io/adk-docs/sessions/state/)
````

## File: 6-persistent-storage/memory_agent/__init__.py
````python

````

## File: 6-persistent-storage/memory_agent/agent.py
````python
from google.adk.agents import Agent
from google.adk.tools.tool_context import ToolContext


def add_reminder(reminder: str, tool_context: ToolContext) -> dict:

    print(f"--- Tool: add_reminder called for '{reminder}' ---")


    reminders = tool_context.state.get("reminders", [])


    reminders.append(reminder)


    tool_context.state["reminders"] = reminders

    return {
        "action": "add_reminder",
        "reminder": reminder,
        "message": f"Added reminder: {reminder}",
    }


def view_reminders(tool_context: ToolContext) -> dict:

    print("--- Tool: view_reminders called ---")


    reminders = tool_context.state.get("reminders", [])

    return {"action": "view_reminders", "reminders": reminders, "count": len(reminders)}


def update_reminder(index: int, updated_text: str, tool_context: ToolContext) -> dict:

    print(
        f"--- Tool: update_reminder called for index {index} with '{updated_text}' ---"
    )


    reminders = tool_context.state.get("reminders", [])


    if not reminders or index < 1 or index > len(reminders):
        return {
            "action": "update_reminder",
            "status": "error",
            "message": f"Could not find reminder at position {index}. Currently there are {len(reminders)} reminders.",
        }


    old_reminder = reminders[index - 1]
    reminders[index - 1] = updated_text


    tool_context.state["reminders"] = reminders

    return {
        "action": "update_reminder",
        "index": index,
        "old_text": old_reminder,
        "updated_text": updated_text,
        "message": f"Updated reminder {index} from '{old_reminder}' to '{updated_text}'",
    }


def delete_reminder(index: int, tool_context: ToolContext) -> dict:

    print(f"--- Tool: delete_reminder called for index {index} ---")


    reminders = tool_context.state.get("reminders", [])


    if not reminders or index < 1 or index > len(reminders):
        return {
            "action": "delete_reminder",
            "status": "error",
            "message": f"Could not find reminder at position {index}. Currently there are {len(reminders)} reminders.",
        }


    deleted_reminder = reminders.pop(index - 1)


    tool_context.state["reminders"] = reminders

    return {
        "action": "delete_reminder",
        "index": index,
        "deleted_reminder": deleted_reminder,
        "message": f"Deleted reminder {index}: '{deleted_reminder}'",
    }


def update_user_name(name: str, tool_context: ToolContext) -> dict:

    print(f"--- Tool: update_user_name called with '{name}' ---")


    old_name = tool_context.state.get("user_name", "")


    tool_context.state["user_name"] = name

    return {
        "action": "update_user_name",
        "old_name": old_name,
        "new_name": name,
        "message": f"Updated your name to: {name}",
    }



memory_agent = Agent(
    name="memory_agent",
    model="gemini-2.0-flash",
    description="A smart reminder agent with persistent memory",
    instruction="""
    You are a friendly reminder assistant that remembers users across conversations.

    The user's information is stored in state:
    - User's name: {user_name}
    - Reminders: {reminders}

    You can help users manage their reminders with the following capabilities:
    1. Add new reminders
    2. View existing reminders
    3. Update reminders
    4. Delete reminders
    5. Update the user's name

    Always be friendly and address the user by name. If you don't know their name yet,
    use the update_user_name tool to store it when they introduce themselves.

    **REMINDER MANAGEMENT GUIDELINES:**

    When dealing with reminders, you need to be smart about finding the right reminder:

    1. When the user asks to update or delete a reminder but doesn't provide an index:
       - If they mention the content of the reminder (e.g., "delete my meeting reminder"),
         look through the reminders to find a match
       - If you find an exact or close match, use that index
       - Never clarify which reminder the user is referring to, just use the first match
       - If no match is found, list all reminders and ask the user to specify

    2. When the user mentions a number or position:
       - Use that as the index (e.g., "delete reminder 2" means index=2)
       - Remember that indexing starts at 1 for the user

    3. For relative positions:
       - Handle "first", "last", "second", etc. appropriately
       - "First reminder" = index 1
       - "Last reminder" = the highest index
       - "Second reminder" = index 2, and so on

    4. For viewing:
       - Always use the view_reminders tool when the user asks to see their reminders
       - Format the response in a numbered list for clarity
       - If there are no reminders, suggest adding some

    5. For addition:
       - Extract the actual reminder text from the user's request
       - Remove phrases like "add a reminder to" or "remind me to"
       - Focus on the task itself (e.g., "add a reminder to buy milk" → add_reminder("buy milk"))

    6. For updates:
       - Identify both which reminder to update and what the new text should be
       - For example, "change my second reminder to pick up groceries" → update_reminder(2, "pick up groceries")

    7. For deletions:
       - Confirm deletion when complete and mention which reminder was removed
       - For example, "I've deleted your reminder to 'buy milk'"

    Remember to explain that you can remember their information across conversations.

    IMPORTANT:
    - use your best judgement to determine which reminder the user is referring to.
    - You don't have to be 100% correct, but try to be as close as possible.
    - Never ask the user to clarify which reminder they are referring to.
    """,
    tools=[
        add_reminder,
        view_reminders,
        update_reminder,
        delete_reminder,
        update_user_name,
    ],
)
````

## File: 6-persistent-storage/main.py
````python
import asyncio

from dotenv import load_dotenv
from google.adk.runners import Runner
from google.adk.sessions import DatabaseSessionService
from memory_agent.agent import memory_agent
from utils import call_agent_async

load_dotenv()



db_url = "sqlite:///./my_agent_data.db"
session_service = DatabaseSessionService(db_url=db_url)




initial_state = {
    "user_name": "Brandon Hancock",
    "reminders": [],
}


async def main_async():

    APP_NAME = "Memory Agent"
    USER_ID = "aiwithbrandon"



    existing_sessions = session_service.list_sessions(
        app_name=APP_NAME,
        user_id=USER_ID,
    )


    if existing_sessions and len(existing_sessions.sessions) > 0:
        # Use the most recent session
        SESSION_ID = existing_sessions.sessions[0].id
        print(f"Continuing existing session: {SESSION_ID}")
    else:
        # Create a new session with initial state
        new_session = session_service.create_session(
            app_name=APP_NAME,
            user_id=USER_ID,
            state=initial_state,
        )
        SESSION_ID = new_session.id
        print(f"Created new session: {SESSION_ID}")

    # ===== PART 4: Agent Runner Setup =====
    # Create a runner with the memory agent
    runner = Runner(
        agent=memory_agent,
        app_name=APP_NAME,
        session_service=session_service,
    )

    # ===== PART 5: Interactive Conversation Loop =====
    print("\nWelcome to Memory Agent Chat!")
    print("Your reminders will be remembered across conversations.")
    print("Type 'exit' or 'quit' to end the conversation.\n")

    while True:

        user_input = input("You: ")


        if user_input.lower() in ["exit", "quit"]:
            print("Ending conversation. Your data has been saved to the database.")
            break


        await call_agent_async(runner, USER_ID, SESSION_ID, user_input)


if __name__ == "__main__":
    asyncio.run(main_async())
````

## File: 6-persistent-storage/README.md
````markdown
# Persistent Storage in ADK

This example demonstrates how to implement persistent storage for your ADK agents, allowing them to remember information and maintain conversation history across multiple sessions, application restarts, and even server deployments.

## What is Persistent Storage in ADK?

In previous examples, we used `InMemorySessionService` which stores session data only in memory - this data is lost when the application stops. For real-world applications, you'll often need your agents to remember user information and conversation history long-term. This is where persistent storage comes in.

ADK provides the `DatabaseSessionService` that allows you to store session data in a SQL database, ensuring:

1. **Long-term Memory**: Information persists across application restarts
2. **Consistent User Experiences**: Users can continue conversations where they left off
3. **Multi-user Support**: Different users' data remains separate and secure
4. **Scalability**: Works with production databases for high-scale deployments

This example shows how to implement a reminder agent that remembers your name and todos across different conversations using an SQLite database.

## Project Structure

```
5-persistent-storage/
│
├── memory_agent/               # Agent package
│   ├── __init__.py             # Required for ADK to discover the agent
│   └── agent.py                # Agent definition with reminder tools
│
├── main.py                     # Application entry point with database session setup
├── utils.py                    # Utility functions for terminal UI and agent interaction
├── .env                        # Environment variables
├── my_agent_data.db            # SQLite database file (created when first run)
└── README.md                   # This documentation
```

## Key Components

### 1. DatabaseSessionService

The core component that provides persistence is the `DatabaseSessionService`, which is initialized with a database URL:

```python
from google.adk.sessions import DatabaseSessionService

db_url = "sqlite:///./my_agent_data.db"
session_service = DatabaseSessionService(db_url=db_url)
```

This service allows ADK to:
- Store session data in a SQLite database file
- Retrieve previous sessions for a user
- Automatically manage database schemas

### 2. Session Management

The example demonstrates proper session management:

```python
# Check for existing sessions for this user
existing_sessions = session_service.list_sessions(
    app_name=APP_NAME,
    user_id=USER_ID,
)

# If there's an existing session, use it, otherwise create a new one
if existing_sessions and len(existing_sessions.sessions) > 0:
    # Use the most recent session
    SESSION_ID = existing_sessions.sessions[0].id
    print(f"Continuing existing session: {SESSION_ID}")
else:
    # Create a new session with initial state
    session_service.create_session(
        app_name=APP_NAME,
        user_id=USER_ID,
        session_id=SESSION_ID,
        state=initialize_state(),
    )
```

### 3. State Management with Tools

The agent includes tools that update the persistent state:

```python
def add_reminder(reminder: str, tool_context: ToolContext) -> dict:
    # Get current reminders from state
    reminders = tool_context.state.get("reminders", [])
    
    # Add the new reminder
    reminders.append(reminder)
    
    # Update state with the new list of reminders
    tool_context.state["reminders"] = reminders
    
    return {
        "action": "add_reminder",
        "reminder": reminder,
        "message": f"Added reminder: {reminder}",
    }
```

Each change to `tool_context.state` is automatically saved to the database.

## Getting Started

### Prerequisites

- Python 3.9+
- Google API Key for Gemini models
- SQLite (included with Python)

### Setup

1. Activate the virtual environment from the root directory:
```bash
# macOS/Linux:
source ../.venv/bin/activate
# Windows CMD:
..\.venv\Scripts\activate.bat
# Windows PowerShell:
..\.venv\Scripts\Activate.ps1
```

2. Make sure your Google API key is set in the `.env` file:
```
GOOGLE_API_KEY=your_api_key_here
```

### Running the Example

To run the persistent storage example:

```bash
python main.py
```

This will:
1. Connect to the SQLite database (or create it if it doesn't exist)
2. Check for previous sessions for the user
3. Start a conversation with the memory agent
4. Save all interactions to the database

### Example Interactions

Try these interactions to test the agent's persistent memory:

1. **First run:**
   - "What's my name?"
   - "My name is John"
   - "Add a reminder to buy groceries"
   - "Add another reminder to finish the report"
   - "What are my reminders?"
   - Exit the program with "exit"

2. **Second run:**
   - "What's my name?"
   - "What reminders do I have?"
   - "Update my second reminder to submit the report by Friday"
   - "Delete the first reminder"
   
The agent will remember your name and reminders between runs!

## Using Database Storage in Production

While this example uses SQLite for simplicity, `DatabaseSessionService` supports various database backends through SQLAlchemy:

- PostgreSQL: `postgresql://user:password@localhost/dbname`
- MySQL: `mysql://user:password@localhost/dbname`
- MS SQL Server: `mssql://user:password@localhost/dbname`

For production use:
1. Choose a database system that meets your scalability needs
2. Configure connection pooling for efficiency
3. Implement proper security for database credentials
4. Consider database backups for critical agent data

## Additional Resources

- [ADK Sessions Documentation](https://google.github.io/adk-docs/sessions/session/)
- [Session Service Implementations](https://google.github.io/adk-docs/sessions/session/#sessionservice-implementations)
- [State Management in ADK](https://google.github.io/adk-docs/sessions/state/)
- [SQLAlchemy Documentation](https://docs.sqlalchemy.org/) for advanced database configuration
````

## File: 6-persistent-storage/utils.py
````python
from google.genai import types



class Colors:
    RESET = "\033[0m"
    BOLD = "\033[1m"
    UNDERLINE = "\033[4m"


    BLACK = "\033[30m"
    RED = "\033[31m"
    GREEN = "\033[32m"
    YELLOW = "\033[33m"
    BLUE = "\033[34m"
    MAGENTA = "\033[35m"
    CYAN = "\033[36m"
    WHITE = "\033[37m"


    BG_BLACK = "\033[40m"
    BG_RED = "\033[41m"
    BG_GREEN = "\033[42m"
    BG_YELLOW = "\033[43m"
    BG_BLUE = "\033[44m"
    BG_MAGENTA = "\033[45m"
    BG_CYAN = "\033[46m"
    BG_WHITE = "\033[47m"


def display_state(
    session_service, app_name, user_id, session_id, label="Current State"
):

    try:
        session = session_service.get_session(
            app_name=app_name, user_id=user_id, session_id=session_id
        )


        print(f"\n{'-' * 10} {label} {'-' * 10}")


        user_name = session.state.get("user_name", "Unknown")
        print(f"👤 User: {user_name}")


        reminders = session.state.get("reminders", [])
        if reminders:
            print("📝 Reminders:")
            for idx, reminder in enumerate(reminders, 1):
                print(f"  {idx}. {reminder}")
        else:
            print("📝 Reminders: None")

        print("-" * (22 + len(label)))
    except Exception as e:
        print(f"Error displaying state: {e}")


async def process_agent_response(event):


    print(f"Event ID: {event.id}, Author: {event.author}")


    has_specific_part = False
    if event.content and event.content.parts:
        for part in event.content.parts:
            if hasattr(part, "executable_code") and part.executable_code:

                print(
                    f"  Debug: Agent generated code:\n```python\n{part.executable_code.code}\n```"
                )
                has_specific_part = True
            elif hasattr(part, "code_execution_result") and part.code_execution_result:

                print(
                    f"  Debug: Code Execution Result: {part.code_execution_result.outcome} - Output:\n{part.code_execution_result.output}"
                )
                has_specific_part = True
            elif hasattr(part, "tool_response") and part.tool_response:

                print(f"  Tool Response: {part.tool_response.output}")
                has_specific_part = True

            elif hasattr(part, "text") and part.text and not part.text.isspace():
                print(f"  Text: '{part.text.strip()}'")


    final_response = None
    if event.is_final_response():
        if (
            event.content
            and event.content.parts
            and hasattr(event.content.parts[0], "text")
            and event.content.parts[0].text
        ):
            final_response = event.content.parts[0].text.strip()

            print(
                f"\n{Colors.BG_BLUE}{Colors.WHITE}{Colors.BOLD}╔══ AGENT RESPONSE ═════════════════════════════════════════{Colors.RESET}"
            )
            print(f"{Colors.CYAN}{Colors.BOLD}{final_response}{Colors.RESET}")
            print(
                f"{Colors.BG_BLUE}{Colors.WHITE}{Colors.BOLD}╚═════════════════════════════════════════════════════════════{Colors.RESET}\n"
            )
        else:
            print(
                f"\n{Colors.BG_RED}{Colors.WHITE}{Colors.BOLD}==> Final Agent Response: [No text content in final event]{Colors.RESET}\n"
            )

    return final_response


async def call_agent_async(runner, user_id, session_id, query):

    content = types.Content(role="user", parts=[types.Part(text=query)])
    print(
        f"\n{Colors.BG_GREEN}{Colors.BLACK}{Colors.BOLD}--- Running Query: {query} ---{Colors.RESET}"
    )
    final_response_text = None


    display_state(
        runner.session_service,
        runner.app_name,
        user_id,
        session_id,
        "State BEFORE processing",
    )

    try:
        async for event in runner.run_async(
            user_id=user_id, session_id=session_id, new_message=content
        ):

            response = await process_agent_response(event)
            if response:
                final_response_text = response
    except Exception as e:
        print(f"Error during agent call: {e}")


    display_state(
        runner.session_service,
        runner.app_name,
        user_id,
        session_id,
        "State AFTER processing",
    )

    return final_response_text
````

## File: 7-multi-agent/manager/sub_agents/funny_nerd/agent.py
````python
from google.adk.agents import Agent
from google.adk.tools.tool_context import ToolContext


def get_nerd_joke(topic: str, tool_context: ToolContext) -> dict:

    print(f"--- Tool: get_nerd_joke called for topic: {topic} ---")


    jokes = {
        "python": "Why don't Python programmers like to use inheritance? Because they don't like to inherit anything!",
        "javascript": "Why did the JavaScript developer go broke? Because he used up all his cache!",
        "java": "Why do Java developers wear glasses? Because they can't C#!",
        "programming": "Why do programmers prefer dark mode? Because light attracts bugs!",
        "math": "Why was the equal sign so humble? Because he knew he wasn't less than or greater than anyone else!",
        "physics": "Why did the photon check a hotel? Because it was travelling light!",
        "chemistry": "Why did the acid go to the gym? To become a buffer solution!",
        "biology": "Why did the cell go to therapy? Because it had too many issues!",
        "default": "Why did the computer go to the doctor? Because it had a virus!",
    }

    joke = jokes.get(topic.lower(), jokes["default"])


    tool_context.state["last_joke_topic"] = topic

    return {"status": "success", "joke": joke, "topic": topic}



funny_nerd = Agent(
    name="funny_nerd",
    model="gemini-2.0-flash",
    description="An agent that tells nerdy jokes about various topics.",
    instruction="""
    You are a funny nerd agent that tells nerdy jokes about various topics.

    When asked to tell a joke:
    1. Use the get_nerd_joke tool to fetch a joke about the requested topic
    2. If no specific topic is mentioned, ask the user what kind of nerdy joke they'd like to hear
    3. Format the response to include both the joke and a brief explanation if needed

    Available topics include:
    - python
    - javascript
    - java
    - programming
    - math
    - physics
    - chemistry
    - biology

    Example response format:
    "Here's a nerdy joke about <TOPIC>:
    <JOKE>

    Explanation: {brief explanation if needed}"

    If the user asks about anything else,
    you should delegate the task to the manager agent.
    """,
    tools=[get_nerd_joke],
)
````

## File: 7-multi-agent/manager/sub_agents/news_analyst/agent.py
````python
from google.adk.agents import Agent
from google.adk.tools import google_search

news_analyst = Agent(
    name="news_analyst",
    model="gemini-2.0-flash",
    description="News analyst agent",
    instruction="""
    You are a helpful assistant that can analyze news articles and provide a summary of the news.

    When asked about news, you should use the google_search tool to search for the news.

    If the user ask for news using a relative time, you should use the get_current_time tool to get the current time to use in the search query.
    """,
    tools=[google_search],
)
````

## File: 7-multi-agent/manager/sub_agents/stock_analyst/agent.py
````python
from datetime import datetime

import yfinance as yf
from google.adk.agents import Agent


def get_stock_price(ticker: str) -> dict:

    print(f"--- Tool: get_stock_price called for {ticker} ---")

    try:

        stock = yf.Ticker(ticker)
        current_price = stock.info.get("currentPrice")

        if current_price is None:
            return {
                "status": "error",
                "error_message": f"Could not fetch price for {ticker}",
            }


        current_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")

        return {
            "status": "success",
            "ticker": ticker,
            "price": current_price,
            "timestamp": current_time,
        }

    except Exception as e:
        return {
            "status": "error",
            "error_message": f"Error fetching stock data: {str(e)}",
        }



stock_analyst = Agent(
    name="stock_analyst",
    model="gemini-2.0-flash",
    description="An agent that can look up stock prices and track them over time.",
    instruction="""
    You are a helpful stock market assistant that helps users track their stocks of interest.

    When asked about stock prices:
    1. Use the get_stock_price tool to fetch the latest price for the requested stock(s)
    2. Format the response to show each stock's current price and the time it was fetched
    3. If a stock price couldn't be fetched, mention this in your response

    Example response format:
    "Here are the current prices for your stocks:
    - GOOG: $175.34 (updated at 2024-04-21 16:30:00)
    - TSLA: $156.78 (updated at 2024-04-21 16:30:00)
    - META: $123.45 (updated at 2024-04-21 16:30:00)"
    """,
    tools=[get_stock_price],
)
````

## File: 7-multi-agent/manager/tools/tools.py
````python
from datetime import datetime


def get_current_time() -> dict:

    return {
        "current_time": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
    }
````

## File: 7-multi-agent/manager/__init__.py
````python
from . import agent
````

## File: 7-multi-agent/manager/agent.py
````python
from google.adk.agents import Agent
from google.adk.tools.agent_tool import AgentTool

from .sub_agents.funny_nerd.agent import funny_nerd
from .sub_agents.news_analyst.agent import news_analyst
from .sub_agents.stock_analyst.agent import stock_analyst
from .tools.tools import get_current_time

root_agent = Agent(
    name="manager",
    model="gemini-2.0-flash",
    description="Manager agent",
    instruction="""
    You are a manager agent that is responsible for overseeing the work of the other agents.

    Always delegate the task to the appropriate agent. Use your best judgement
    to determine which agent to delegate to.

    You are responsible for delegating tasks to the following agent:
    - stock_analyst
    - funny_nerd

    You also have access to the following tools:
    - news_analyst
    - get_current_time
    """,
    sub_agents=[stock_analyst, funny_nerd],
    tools=[
        AgentTool(news_analyst),
        get_current_time,
    ],
)
````

## File: 7-multi-agent/README.md
````markdown
# Multi-Agent Systems in ADK

This example demonstrates how to create a multi-agent system in ADK, where specialized agents collaborate to handle complex tasks, each focusing on their area of expertise.

## What is a Multi-Agent System?

A Multi-Agent System is an advanced pattern in the Agent Development Kit (ADK) that allows multiple specialized agents to work together to handle complex tasks. Each agent can focus on a specific domain or functionality, and they can collaborate through delegation and communication to solve problems that would be difficult for a single agent.

## Project Structure Requirements

For multi-agent systems to work properly with ADK, your project must follow a specific structure:

```
parent_folder/
├── root_agent_folder/           # Main agent package (e.g., "manager")
│   ├── __init__.py              # Must import agent.py
│   ├── agent.py                 # Must define root_agent
│   ├── .env                     # Environment variables
│   └── sub_agents/              # Directory for all sub-agents
│       ├── __init__.py          # Empty or imports sub-agents
│       ├── agent_1_folder/      # Sub-agent package
│       │   ├── __init__.py      # Must import agent.py
│       │   └── agent.py         # Must define an agent variable
│       ├── agent_2_folder/
│       │   ├── __init__.py
│       │   └── agent.py
│       └── ...
```

### Essential Structure Components:

1. **Root Agent Package**
   - Must have the standard agent structure (like in the basic agent example)
   - The `agent.py` file must define a `root_agent` variable

2. **Sub-agents Directory**
   - Typically organized as a directory called `sub_agents` inside the root agent folder
   - Each sub-agent should be in its own directory following the same structure as regular agents

3. **Importing Sub-agents**
   - Root agent must import sub-agents to use them:
   ```python
   from .sub_agents.funny_nerd.agent import funny_nerd
   from .sub_agents.stock_analyst.agent import stock_analyst
   ```

4. **Command Location**
   - Always run `adk web` from the parent directory (`6-multi-agent`), not from inside any agent directory

This structure ensures that ADK can discover and correctly load all agents in the hierarchy.

## Multi-Agent Architecture Options

ADK offers two primary approaches to building multi-agent systems:

### 1. Sub-Agent Delegation Model

Using the `sub_agents` parameter, the root agent can fully delegate tasks to specialized agents:

```python
root_agent = Agent(
    name="manager",
    model="gemini-2.0-flash",
    description="Manager agent",
    instruction="You are a manager agent that delegates tasks to specialized agents...",
    sub_agents=[stock_analyst, funny_nerd],
)
```

**Characteristics:**
- Complete delegation - sub-agent takes over the entire response
- The sub-agent decision is final and takes control of the conversation
- Root agent acts as a "router" determining which specialist should handle the query

### 2. Agent-as-a-Tool Model

Using the `AgentTool` wrapper, agents can be used as tools by other agents:

```python
from google.adk.tools.agent_tool import AgentTool

root_agent = Agent(
    name="manager",
    model="gemini-2.0-flash",
    description="Manager agent",
    instruction="You are a manager agent that uses specialized agents as tools...",
    tools=[
        AgentTool(news_analyst),
        get_current_time,
    ],
)
```

**Characteristics:**
- Sub-agent returns results to the root agent
- Root agent maintains control and can incorporate the sub-agent's response into its own
- Multiple tool calls can be made to different agent tools in a single response
- Gives the root agent more flexibility in how it uses the results

## Limitations When Using Multi-Agents

### Sub-agent Restrictions

**Built-in tools cannot be used within a sub-agent.**

For example, this approach using built-in tools within sub-agents is **not** currently supported:

```python
search_agent = Agent(
    model='gemini-2.0-flash',
    name='SearchAgent',
    instruction="You're a specialist in Google Search",
    tools=[google_search],  # Built-in tool
)
coding_agent = Agent(
    model='gemini-2.0-flash',
    name='CodeAgent',
    instruction="You're a specialist in Code Execution",
    tools=[built_in_code_execution],  # Built-in tool
)
root_agent = Agent(
    name="RootAgent",
    model="gemini-2.0-flash",
    description="Root Agent",
    sub_agents=[
        search_agent,  # NOT SUPPORTED
        coding_agent   # NOT SUPPORTED
    ],
)
```

### Workaround Using Agent Tools

To use multiple built-in tools or to combine built-in tools with other tools, you can use the `AgentTool` approach:

```python
from google.adk.tools import agent_tool

search_agent = Agent(
    model='gemini-2.0-flash',
    name='SearchAgent',
    instruction="You're a specialist in Google Search",
    tools=[google_search],
)
coding_agent = Agent(
    model='gemini-2.0-flash',
    name='CodeAgent',
    instruction="You're a specialist in Code Execution",
    tools=[built_in_code_execution],
)
root_agent = Agent(
    name="RootAgent",
    model="gemini-2.0-flash",
    description="Root Agent",
    tools=[
        agent_tool.AgentTool(agent=search_agent), 
        agent_tool.AgentTool(agent=coding_agent)
    ],
)
```

This approach wraps agents as tools, allowing the root agent to delegate to specialized agents that each use a single built-in tool.

## Our Multi-Agent Example

This example implements a manager agent that works with three specialized agents:

1. **Stock Analyst** (Sub-agent): Provides financial information and stock market insights
2. **Funny Nerd** (Sub-agent): Creates nerdy jokes about technical topics
3. **News Analyst** (Agent Tool): Gives summaries of current technology news

The manager agent routes queries to the appropriate specialist based on the content of the user's request.

## Getting Started

This example uses the same virtual environment created in the root directory. Make sure you have:

1. Activated the virtual environment from the root directory:
```bash
# macOS/Linux:
source ../.venv/bin/activate
# Windows CMD:
..\.venv\Scripts\activate.bat
# Windows PowerShell:
..\.venv\Scripts\Activate.ps1
```

2. Set up your API key:
   - Rename `.env.example` to `.env` in the manager folder
   - Add your Google API key to the `GOOGLE_API_KEY` variable in the `.env` file

## Running the Example

To run the multi-agent example:

1. Navigate to the 6-multi-agent directory containing your agent folders.

2. Start the interactive web UI:
```bash
adk web
```

3. Access the web UI by opening the URL shown in your terminal (typically http://localhost:8000)

4. Select the "manager" agent from the dropdown menu in the top-left corner of the UI

5. Start chatting with your agent in the textbox at the bottom of the screen

### Troubleshooting

If your multi-agent setup doesn't appear properly in the dropdown menu:
- Make sure you're running `adk web` from the parent directory (6-multi-agent)
- Verify that each agent's `__init__.py` properly imports its respective `agent.py`
- Check that the root agent properly imports all sub-agents

### Example Prompts to Try

- "Can you tell me about the stock market today?"
- "Tell me something funny about programming"
- "What's the latest tech news?"
- "What time is it right now?"

You can exit the conversation or stop the server by pressing `Ctrl+C` in your terminal.

## Additional Resources

- [ADK Multi-Agent Systems Documentation](https://google.github.io/adk-docs/agents/multi-agent-systems/)
- [Agent Tools Documentation](https://google.github.io/adk-docs/tools/function-tools/#3-agent-as-a-tool)
````

## File: 8-stateful-multi-agent/customer_service_agent/sub_agents/course_support_agent/__init__.py
````python
from .agent import course_support_agent

__all__ = ["course_support_agent"]
````

## File: 8-stateful-multi-agent/customer_service_agent/sub_agents/course_support_agent/agent.py
````python
from google.adk.agents import Agent


course_support_agent = Agent(
    name="course_support",
    model="gemini-2.0-flash",
    description="Course support agent for the AI Marketing Platform course",
    instruction="""
    You are the course support agent for the Fullstack AI Marketing Platform course.
    Your role is to help users with questions about course content and sections.

    <user_info>
    Name: {user_name}
    </user_info>

    <purchase_info>
    Purchased Courses: {purchased_courses}
    </purchase_info>

    Before helping:
    - Check if the user owns the AI Marketing Platform course
    - Course information is stored as objects with "id" and "purchase_date" properties
    - Look for a course with id "ai_marketing_platform" in the purchased courses
    - Only provide detailed help if they own the course
    - If they don't own the course, direct them to the sales agent
    - If they do own the course, you can mention when they purchased it (from the purchase_date property)

    Course Sections:
    1. Introduction
       - Course Overview
       - Tech Stack Introduction
       - Project Goals

    2. Problem, Solution, & Technical Design
       - Market Analysis
       - Architecture Overview
       - Tech Stack Selection

    3. Models & Views - How To Think
       - Data Modeling
       - View Structure
       - Component Design

    4. Setup Environment
       - Development Tools
       - Configuration
       - Dependencies

    5. Create Projects
       - Project Structure
       - Initial Setup
       - Basic Configuration

    6. Software Deployment Tools
       - Deployment Options
       - CI/CD Setup
       - Monitoring

    7. NextJS Crash Course
       - Fundamentals
       - Routing
       - API Routes

    8. Stub Out NextJS App
       - Create app directory structure
       - Setup initial layouts
       - Configure NextJS routing
       - Create placeholder components

    9. Create Responsive Sidebar
       - Design mobile-friendly sidebar
       - Implement sidebar navigation
       - Add responsive breakpoints
       - Create menu toggling behavior

    10. Setup Auth with Clerk
       - Integrate Clerk authentication
       - Create login/signup flows
       - Configure protected routes
       - Setup user session management

    11. Setup Postgres Database & Blob Storage
       - Configure database connections
       - Create schema and migrations
       - Setup file/image storage
       - Implement data access patterns

    12. Projects Build Out (List & Detail)
       - Create projects listing page
       - Implement project detail views
       - Add CRUD operations for projects
       - Create data fetching hooks

    13. Asset Processing NextJS
       - Client-side image optimization
       - Asset loading strategies
       - Implementing CDN integration
       - Frontend caching mechanisms

    14. Asset Processing Server
       - Server-side image manipulation
       - Batch processing workflows
       - Compression and optimization
       - Storage management solutions

    15. Prompt Management
       - Create prompt templates
       - Build prompt versioning system
       - Implement prompt testing tools
       - Design prompt chaining capabilities

    16. Fully Build Template (List & Detail)
       - Create template management system
       - Implement template editor
       - Design template marketplace
       - Add template sharing features

    17. AI Content Generation
       - Integrate AI generation capabilities
       - Design content generation workflows
       - Create output validation systems
       - Implement feedback mechanisms

    18. Setup Stripe + Block Free Users
       - Integrate Stripe payment processing
       - Create subscription management
       - Implement payment webhooks
       - Design feature access restrictions

    19. Landing & Pricing Pages
       - Design conversion-optimized landing pages
       - Create pricing tier comparisons
       - Implement checkout flows
       - Add testimonials and social proof

    When helping:
    1. Direct users to specific sections
    2. Explain concepts clearly
    3. Provide context for how sections connect
    4. Encourage hands-on practice
    """,
    tools=[],
)
````

## File: 8-stateful-multi-agent/customer_service_agent/sub_agents/order_agent/agent.py
````python
from datetime import datetime

from google.adk.agents import Agent
from google.adk.tools.tool_context import ToolContext


def get_current_time() -> dict:

    return {
        "current_time": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
    }


def refund_course(tool_context: ToolContext) -> dict:

    course_id = "ai_marketing_platform"
    current_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")


    current_purchased_courses = tool_context.state.get("purchased_courses", [])


    course_ids = [
        course["id"] for course in current_purchased_courses if isinstance(course, dict)
    ]
    if course_id not in course_ids:
        return {
            "status": "error",
            "message": "You don't own this course, so it can't be refunded.",
        }


    new_purchased_courses = []
    for course in current_purchased_courses:

        if not course or not isinstance(course, dict):
            continue

        if course.get("id") == course_id:
            continue

        new_purchased_courses.append(course)


    tool_context.state["purchased_courses"] = new_purchased_courses


    current_interaction_history = tool_context.state.get("interaction_history", [])


    new_interaction_history = current_interaction_history.copy()
    new_interaction_history.append(
        {"action": "refund_course", "course_id": course_id, "timestamp": current_time}
    )


    tool_context.state["interaction_history"] = new_interaction_history

    return {
        "status": "success",
        "message": """Successfully refunded the AI Marketing Platform course!
         Your $149 will be returned to your original payment method within 3-5 business days.""",
        "course_id": course_id,
        "timestamp": current_time,
    }



order_agent = Agent(
    name="order_agent",
    model="gemini-2.0-flash",
    description="Order agent for viewing purchase history and processing refunds",
    instruction="""
    You are the order agent for the AI Developer Accelerator community.
    Your role is to help users view their purchase history, course access, and process refunds.

    <user_info>
    Name: {user_name}
    </user_info>

    <purchase_info>
    Purchased Courses: {purchased_courses}
    </purchase_info>

    <interaction_history>
    {interaction_history}
    </interaction_history>

    When users ask about their purchases:
    1. Check their course list from the purchase info above
       - Course information is stored as objects with "id" and "purchase_date" properties
    2. Format the response clearly showing:
       - Which courses they own
       - When they were purchased (from the course.purchase_date property)

    When users request a refund:
    1. Verify they own the course they want to refund ("ai_marketing_platform")
    2. If they own it:
       - Use the refund_course tool to process the refund
       - Confirm the refund was successful
       - Remind them the money will be returned to their original payment method
       - If it's been more than 30 days, inform them that they are not eligible for a refund
    3. If they don't own it:
       - Inform them they don't own the course, so no refund is needed

    Course Information:
    - ai_marketing_platform: "Fullstack AI Marketing Platform" ($149)

    Example Response for Purchase History:
    "Here are your purchased courses:
    1. Fullstack AI Marketing Platform
       - Purchased on: 2024-04-21 10:30:00
       - Full lifetime access"

    Example Response for Refund:
    "I've processed your refund for the Fullstack AI Marketing Platform course.
    Your $149 will be returned to your original payment method within 3-5 business days.
    The course has been removed from your account."

    If they haven't purchased any courses:
    - Let them know they don't have any courses yet
    - Suggest talking to the sales agent about the AI Marketing Platform course

    Remember:
    - Be clear and professional
    - Mention our 30-day money-back guarantee if relevant
    - Direct course questions to course support
    - Direct purchase inquiries to sales
    """,
    tools=[refund_course, get_current_time],
)
````

## File: 8-stateful-multi-agent/customer_service_agent/sub_agents/policy_agent/__init__.py
````python
from .agent import policy_agent

__all__ = ["policy_agent"]
````

## File: 8-stateful-multi-agent/customer_service_agent/sub_agents/policy_agent/agent.py
````python
from google.adk.agents import Agent


policy_agent = Agent(
    name="policy_agent",
    model="gemini-2.0-flash",
    description="Policy agent for the AI Developer Accelerator community",
    instruction="""
    You are the policy agent for the AI Developer Accelerator community. Your role is to help users
    understand our community guidelines and policies.

    <user_info>
    Name: {user_name}
    </user_info>

    Community Guidelines:
    1. Promotions
       - No self-promotion or advertising
       - Focus on learning and growing together
       - Share your work only in designated channels

    2. Content Quality
       - Provide detailed, helpful responses
       - Include code examples when relevant
       - Use proper formatting for code snippets

    3. Behavior
       - Be respectful and professional
       - No politics or religion discussions
       - Help maintain a positive learning environment

    Course Policies:
    1. Refund Policy
       - 30-day money-back guarantee
       - Full refund if you complete the course and aren't satisfied
       - No questions asked

    2. Course Access
       - Lifetime access to course content
       - 6 weeks of group support included
       - Weekly coaching calls every Sunday

    3. Code Usage
       - You can use course code in your projects
       - Credit not required but appreciated
       - No reselling of course materials

    Privacy Policy:
    - We respect your privacy
    - Your data is never sold
    - Course progress is tracked for support purposes

    When responding:
    1. Be clear and direct
    2. Quote relevant policy sections
    3. Explain the reasoning behind policies
    4. Direct complex issues to support
    """,
    tools=[],
)
````

## File: 8-stateful-multi-agent/customer_service_agent/sub_agents/sales_agent/__init__.py
````python
from .agent import sales_agent

__all__ = ["sales_agent"]
````

## File: 8-stateful-multi-agent/customer_service_agent/sub_agents/sales_agent/agent.py
````python
from datetime import datetime

from google.adk.agents import Agent
from google.adk.tools.tool_context import ToolContext


def purchase_course(tool_context: ToolContext) -> dict:

    course_id = "ai_marketing_platform"
    current_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")


    current_purchased_courses = tool_context.state.get("purchased_courses", [])


    course_ids = [
        course["id"] for course in current_purchased_courses if isinstance(course, dict)
    ]
    if course_id in course_ids:
        return {"status": "error", "message": "You already own this course!"}


    new_purchased_courses = []

    for course in current_purchased_courses:
        if isinstance(course, dict) and "id" in course:
            new_purchased_courses.append(course)


    new_purchased_courses.append({"id": course_id, "purchase_date": current_time})


    tool_context.state["purchased_courses"] = new_purchased_courses


    current_interaction_history = tool_context.state.get("interaction_history", [])


    new_interaction_history = current_interaction_history.copy()
    new_interaction_history.append(
        {"action": "purchase_course", "course_id": course_id, "timestamp": current_time}
    )


    tool_context.state["interaction_history"] = new_interaction_history

    return {
        "status": "success",
        "message": "Successfully purchased the AI Marketing Platform course!",
        "course_id": course_id,
        "timestamp": current_time,
    }



sales_agent = Agent(
    name="sales_agent",
    model="gemini-2.0-flash",
    description="Sales agent for the AI Marketing Platform course",
    instruction="""
    You are a sales agent for the AI Developer Accelerator community, specifically handling sales
    for the Fullstack AI Marketing Platform course.

    <user_info>
    Name: {user_name}
    </user_info>

    <purchase_info>
    Purchased Courses: {purchased_courses}
    </purchase_info>

    <interaction_history>
    {interaction_history}
    </interaction_history>

    Course Details:
    - Name: Fullstack AI Marketing Platform
    - Price: $149
    - Value Proposition: Learn to build AI-powered marketing automation apps
    - Includes: 6 weeks of group support with weekly coaching calls

    When interacting with users:
    1. Check if they already own the course (check purchased_courses above)
       - Course information is stored as objects with "id" and "purchase_date" properties
       - The course id is "ai_marketing_platform"
    2. If they own it:
       - Remind them they have access
       - Ask if they need help with any specific part
       - Direct them to course support for content questions

    3. If they don't own it:
       - Explain the course value proposition
       - Mention the price ($149)
       - If they want to purchase:
           - Use the purchase_course tool
           - Confirm the purchase
           - Ask if they'd like to start learning right away

    4. After any interaction:
       - The state will automatically track the interaction
       - Be ready to hand off to course support after purchase

    Remember:
    - Be helpful but not pushy
    - Focus on the value and practical skills they'll gain
    - Emphasize the hands-on nature of building a real AI application
    """,
    tools=[purchase_course],
)
````

## File: 8-stateful-multi-agent/customer_service_agent/__init__.py
````python
from . import agent
````

## File: 8-stateful-multi-agent/customer_service_agent/agent.py
````python
from google.adk.agents import Agent

from .sub_agents.course_support_agent.agent import course_support_agent
from .sub_agents.order_agent.agent import order_agent
from .sub_agents.policy_agent.agent import policy_agent
from .sub_agents.sales_agent.agent import sales_agent


customer_service_agent = Agent(
    name="customer_service",
    model="gemini-2.0-flash",
    description="Customer service agent for AI Developer Accelerator community",
    instruction="""
    You are the primary customer service agent for the AI Developer Accelerator community.
    Your role is to help users with their questions and direct them to the appropriate specialized agent.

    **Core Capabilities:**

    1. Query Understanding & Routing
       - Understand user queries about policies, course purchases, course support, and orders
       - Direct users to the appropriate specialized agent
       - Maintain conversation context using state

    2. State Management
       - Track user interactions in state['interaction_history']
       - Monitor user's purchased courses in state['purchased_courses']
         - Course information is stored as objects with "id" and "purchase_date" properties
       - Use state to provide personalized responses

    **User Information:**
    <user_info>
    Name: {user_name}
    </user_info>

    **Purchase Information:**
    <purchase_info>
    Purchased Courses: {purchased_courses}
    </purchase_info>

    **Interaction History:**
    <interaction_history>
    {interaction_history}
    </interaction_history>

    You have access to the following specialized agents:

    1. Policy Agent
       - For questions about community guidelines, course policies, refunds
       - Direct policy-related queries here

    2. Sales Agent
       - For questions about purchasing the AI Marketing Platform course
       - Handles course purchases and updates state
       - Course price: $149

    3. Course Support Agent
       - For questions about course content
       - Only available for courses the user has purchased
       - Check if a course with id "ai_marketing_platform" exists in the purchased courses before directing here

    4. Order Agent
       - For checking purchase history and processing refunds
       - Shows courses user has bought
       - Can process course refunds (30-day money-back guarantee)
       - References the purchased courses information

    Tailor your responses based on the user's purchase history and previous interactions.
    When the user hasn't purchased any courses yet, encourage them to explore the AI Marketing Platform.
    When the user has purchased courses, offer support for those specific courses.

    When users express dissatisfaction or ask for a refund:
    - Direct them to the Order Agent, which can process refunds
    - Mention our 30-day money-back guarantee policy

    Always maintain a helpful and professional tone. If you're unsure which agent to delegate to,
    ask clarifying questions to better understand the user's needs.
    """,
    sub_agents=[policy_agent, sales_agent, course_support_agent, order_agent],
    tools=[],
)
````

## File: 8-stateful-multi-agent/main.py
````python
import asyncio


from customer_service_agent.agent import customer_service_agent
from dotenv import load_dotenv
from google.adk.runners import Runner
from google.adk.sessions import InMemorySessionService
from utils import add_user_query_to_history, call_agent_async

load_dotenv()



session_service = InMemorySessionService()




initial_state = {
    "user_name": "Brandon Hancock",
    "purchased_courses": [],
    "interaction_history": [],
}


async def main_async():

    APP_NAME = "Customer Support"
    USER_ID = "aiwithbrandon"



    new_session = session_service.create_session(
        app_name=APP_NAME,
        user_id=USER_ID,
        state=initial_state,
    )
    SESSION_ID = new_session.id
    print(f"Created new session: {SESSION_ID}")



    runner = Runner(
        agent=customer_service_agent,
        app_name=APP_NAME,
        session_service=session_service,
    )


    print("\nWelcome to Customer Service Chat!")
    print("Type 'exit' or 'quit' to end the conversation.\n")

    while True:

        user_input = input("You: ")


        if user_input.lower() in ["exit", "quit"]:
            print("Ending conversation. Goodbye!")
            break


        add_user_query_to_history(
            session_service, APP_NAME, USER_ID, SESSION_ID, user_input
        )


        await call_agent_async(runner, USER_ID, SESSION_ID, user_input)



    final_session = session_service.get_session(
        app_name=APP_NAME, user_id=USER_ID, session_id=SESSION_ID
    )
    print("\nFinal Session State:")
    for key, value in final_session.state.items():
        print(f"{key}: {value}")


def main():

    asyncio.run(main_async())


if __name__ == "__main__":
    main()
````

## File: 8-stateful-multi-agent/README.md
````markdown
# Stateful Multi-Agent Systems in ADK

This example demonstrates how to create a stateful multi-agent system in ADK, combining the power of persistent state management with specialized agent delegation. This approach creates intelligent agent systems that remember user information across interactions while leveraging specialized domain expertise.

## What is a Stateful Multi-Agent System?

A Stateful Multi-Agent System combines two powerful patterns:

1. **State Management**: Persisting information about users and conversations across interactions
2. **Multi-Agent Architecture**: Distributing tasks among specialized agents based on their expertise

The result is a sophisticated agent ecosystem that can:
- Remember user information and interaction history
- Route queries to the most appropriate specialized agent
- Provide personalized responses based on past interactions
- Maintain context across multiple agent delegates

This example implements a customer service system for an online course platform, where specialized agents handle different aspects of customer support while sharing a common state.

## Project Structure

```
7-stateful-multi-agent/
│
├── customer_service_agent/         # Main agent package
│   ├── __init__.py                 # Required for ADK discovery
│   ├── agent.py                    # Root agent definition
│   └── sub_agents/                 # Specialized agents
│       ├── course_support_agent/   # Handles course content questions
│       ├── order_agent/            # Manages order history and refunds
│       ├── policy_agent/           # Answers policy questions
│       └── sales_agent/            # Handles course purchases
│
├── main.py                         # Application entry point with session setup
├── utils.py                        # Helper functions for state management
├── .env                            # Environment variables
└── README.md                       # This documentation
```

## Key Components

### 1. Session Management

The example uses `InMemorySessionService` to store session state:

```python
session_service = InMemorySessionService()

def initialize_state():
    """Initialize the session state with default values."""
    return {
        "user_name": "Brandon Hancock",
        "purchased_courses": [""],
        "interaction_history": [],
    }

# Create a new session with initial state
session_service.create_session(
    app_name=APP_NAME,
    user_id=USER_ID,
    session_id=SESSION_ID,
    state=initialize_state(),
)
```

### 2. State Sharing Across Agents

All agents in the system can access the same session state, enabling:
- Root agent to track interaction history
- Sales agent to update purchased courses
- Course support agent to check if user has purchased specific courses
- All agents to personalize responses based on user information

### 3. Multi-Agent Delegation

The customer service agent routes queries to specialized sub-agents:

```python
customer_service_agent = Agent(
    name="customer_service",
    model="gemini-2.0-flash",
    description="Customer service agent for AI Developer Accelerator community",
    instruction="""
    You are the primary customer service agent for the AI Developer Accelerator community.
    Your role is to help users with their questions and direct them to the appropriate specialized agent.
    
    # ... detailed instructions ...
    
    """,
    sub_agents=[policy_agent, sales_agent, course_support_agent, order_agent],
    tools=[get_current_time],
)
```

## How It Works

1. **Initial Session Creation**:
   - A new session is created with user information and empty interaction history
   - Session state is initialized with default values

2. **Conversation Tracking**:
   - Each user message is added to `interaction_history` in the state
   - Agents can review past interactions to maintain context

3. **Query Routing**:
   - The root agent analyzes the user query and decides which specialist should handle it
   - Specialized agents receive the full state context when delegated to

4. **State Updates**:
   - When a user purchases a course, the sales agent updates `purchased_courses`
   - These updates are available to all agents for future interactions

5. **Personalized Responses**:
   - Agents tailor responses based on purchase history and previous interactions
   - Different paths are taken based on what the user has already purchased

## Getting Started


### Setup

1. Activate the virtual environment from the root directory:
```bash
# macOS/Linux:
source ../.venv/bin/activate
# Windows CMD:
..\.venv\Scripts\activate.bat
# Windows PowerShell:
..\.venv\Scripts\Activate.ps1
```

2. Make sure your Google API key is set in the `.env` file:
```
GOOGLE_API_KEY=your_api_key_here
```

### Running the Example

To run the stateful multi-agent example:

```bash
python main.py
```

This will:
1. Initialize a new session with default state
2. Start an interactive conversation with the customer service agent
3. Track all interactions in the session state
4. Allow specialized agents to handle specific queries

### Example Conversation Flow

Try this conversation flow to test the system:

1. **Start with a general query**:
   - "What courses do you offer?"
   - (Root agent will route to sales agent)

2. **Ask about purchasing**:
   - "I want to buy the AI Marketing Platform course"
   - (Sales agent will process the purchase and update state)

3. **Ask about course content**:
   - "Can you tell me about the content in the AI Marketing Platform course?"
   - (Root agent will route to course support agent, which now has access)

4. **Ask about refunds**:
   - "What's your refund policy?"
   - (Root agent will route to policy agent)

Notice how the system remembers your purchase across different specialized agents!

## Advanced Features

### 1. Interaction History Tracking

The system maintains a history of interactions to provide context:

```python
# Update interaction history with the user's query
add_user_query_to_history(
    session_service, APP_NAME, USER_ID, SESSION_ID, user_input
)
```

### 2. Dynamic Access Control

The system implements conditional access to certain agents:

```
3. Course Support Agent
   - For questions about course content
   - Only available for courses the user has purchased
   - Check if "ai_marketing_platform" is in the purchased courses before directing here
```

### 3. State-Based Personalization

All agents tailor responses based on session state:

```
Tailor your responses based on the user's purchase history and previous interactions.
When the user hasn't purchased any courses yet, encourage them to explore the AI Marketing Platform.
When the user has purchased courses, offer support for those specific courses.
```

## Production Considerations

For a production implementation, consider:

1. **Persistent Storage**: Replace `InMemorySessionService` with `DatabaseSessionService` to persist state across application restarts
2. **User Authentication**: Implement proper user authentication to securely identify users
3. **Error Handling**: Add robust error handling for agent failures and state corruption
4. **Monitoring**: Implement logging and monitoring to track system performance

## Additional Resources

- [ADK Sessions Documentation](https://google.github.io/adk-docs/sessions/session/)
- [ADK Multi-Agent Systems Documentation](https://google.github.io/adk-docs/agents/multi-agent-systems/)
- [State Management in ADK](https://google.github.io/adk-docs/sessions/state/)
````

## File: 8-stateful-multi-agent/utils.py
````python
from datetime import datetime

from google.genai import types



class Colors:
    RESET = "\033[0m"
    BOLD = "\033[1m"
    UNDERLINE = "\033[4m"


    BLACK = "\033[30m"
    RED = "\033[31m"
    GREEN = "\033[32m"
    YELLOW = "\033[33m"
    BLUE = "\033[34m"
    MAGENTA = "\033[35m"
    CYAN = "\033[36m"
    WHITE = "\033[37m"


    BG_BLACK = "\033[40m"
    BG_RED = "\033[41m"
    BG_GREEN = "\033[42m"
    BG_YELLOW = "\033[43m"
    BG_BLUE = "\033[44m"
    BG_MAGENTA = "\033[45m"
    BG_CYAN = "\033[46m"
    BG_WHITE = "\033[47m"


def update_interaction_history(session_service, app_name, user_id, session_id, entry):

    try:

        session = session_service.get_session(
            app_name=app_name, user_id=user_id, session_id=session_id
        )


        interaction_history = session.state.get("interaction_history", [])


        if "timestamp" not in entry:
            entry["timestamp"] = datetime.now().strftime("%Y-%m-%d %H:%M:%S")


        interaction_history.append(entry)


        updated_state = session.state.copy()
        updated_state["interaction_history"] = interaction_history


        session_service.create_session(
            app_name=app_name,
            user_id=user_id,
            session_id=session_id,
            state=updated_state,
        )
    except Exception as e:
        print(f"Error updating interaction history: {e}")


def add_user_query_to_history(session_service, app_name, user_id, session_id, query):

    update_interaction_history(
        session_service,
        app_name,
        user_id,
        session_id,
        {
            "action": "user_query",
            "query": query,
        },
    )


def add_agent_response_to_history(
    session_service, app_name, user_id, session_id, agent_name, response
):

    update_interaction_history(
        session_service,
        app_name,
        user_id,
        session_id,
        {
            "action": "agent_response",
            "agent": agent_name,
            "response": response,
        },
    )


def display_state(
    session_service, app_name, user_id, session_id, label="Current State"
):

    try:
        session = session_service.get_session(
            app_name=app_name, user_id=user_id, session_id=session_id
        )


        print(f"\n{'-' * 10} {label} {'-' * 10}")


        user_name = session.state.get("user_name", "Unknown")
        print(f"👤 User: {user_name}")


        purchased_courses = session.state.get("purchased_courses", [])
        if purchased_courses and any(purchased_courses):
            print("📚 Courses:")
            for course in purchased_courses:
                if isinstance(course, dict):
                    course_id = course.get("id", "Unknown")
                    purchase_date = course.get("purchase_date", "Unknown date")
                    print(f"  - {course_id} (purchased on {purchase_date})")
                elif course:
                    print(f"  - {course}")
        else:
            print("📚 Courses: None")


        interaction_history = session.state.get("interaction_history", [])
        if interaction_history:
            print("📝 Interaction History:")
            for idx, interaction in enumerate(interaction_history, 1):

                if isinstance(interaction, dict):
                    action = interaction.get("action", "interaction")
                    timestamp = interaction.get("timestamp", "unknown time")

                    if action == "user_query":
                        query = interaction.get("query", "")
                        print(f'  {idx}. User query at {timestamp}: "{query}"')
                    elif action == "agent_response":
                        agent = interaction.get("agent", "unknown")
                        response = interaction.get("response", "")

                        if len(response) > 100:
                            response = response[:97] + "..."
                        print(f'  {idx}. {agent} response at {timestamp}: "{response}"')
                    else:
                        details = ", ".join(
                            f"{k}: {v}"
                            for k, v in interaction.items()
                            if k not in ["action", "timestamp"]
                        )
                        print(
                            f"  {idx}. {action} at {timestamp}"
                            + (f" ({details})" if details else "")
                        )
                else:
                    print(f"  {idx}. {interaction}")
        else:
            print("📝 Interaction History: None")


        other_keys = [
            k
            for k in session.state.keys()
            if k not in ["user_name", "purchased_courses", "interaction_history"]
        ]
        if other_keys:
            print("🔑 Additional State:")
            for key in other_keys:
                print(f"  {key}: {session.state[key]}")

        print("-" * (22 + len(label)))
    except Exception as e:
        print(f"Error displaying state: {e}")


async def process_agent_response(event):

    print(f"Event ID: {event.id}, Author: {event.author}")


    has_specific_part = False
    if event.content and event.content.parts:
        for part in event.content.parts:
            if hasattr(part, "text") and part.text and not part.text.isspace():
                print(f"  Text: '{part.text.strip()}'")


    final_response = None
    if not has_specific_part and event.is_final_response():
        if (
            event.content
            and event.content.parts
            and hasattr(event.content.parts[0], "text")
            and event.content.parts[0].text
        ):
            final_response = event.content.parts[0].text.strip()

            print(
                f"\n{Colors.BG_BLUE}{Colors.WHITE}{Colors.BOLD}╔══ AGENT RESPONSE ═════════════════════════════════════════{Colors.RESET}"
            )
            print(f"{Colors.CYAN}{Colors.BOLD}{final_response}{Colors.RESET}")
            print(
                f"{Colors.BG_BLUE}{Colors.WHITE}{Colors.BOLD}╚═════════════════════════════════════════════════════════════{Colors.RESET}\n"
            )
        else:
            print(
                f"\n{Colors.BG_RED}{Colors.WHITE}{Colors.BOLD}==> Final Agent Response: [No text content in final event]{Colors.RESET}\n"
            )

    return final_response


async def call_agent_async(runner, user_id, session_id, query):

    content = types.Content(role="user", parts=[types.Part(text=query)])
    print(
        f"\n{Colors.BG_GREEN}{Colors.BLACK}{Colors.BOLD}--- Running Query: {query} ---{Colors.RESET}"
    )
    final_response_text = None
    agent_name = None


    display_state(
        runner.session_service,
        runner.app_name,
        user_id,
        session_id,
        "State BEFORE processing",
    )

    try:
        async for event in runner.run_async(
            user_id=user_id, session_id=session_id, new_message=content
        ):

            if event.author:
                agent_name = event.author

            response = await process_agent_response(event)
            if response:
                final_response_text = response
    except Exception as e:
        print(f"{Colors.BG_RED}{Colors.WHITE}ERROR during agent run: {e}{Colors.RESET}")


    if final_response_text and agent_name:
        add_agent_response_to_history(
            runner.session_service,
            runner.app_name,
            user_id,
            session_id,
            agent_name,
            final_response_text,
        )


    display_state(
        runner.session_service,
        runner.app_name,
        user_id,
        session_id,
        "State AFTER processing",
    )

    print(f"{Colors.YELLOW}{'-' * 30}{Colors.RESET}")
    return final_response_text
````

## File: 9-callbacks/before_after_agent/__init__.py
````python
from . import agent
````

## File: 9-callbacks/before_after_agent/.env.example
````
GOOGLE_GENAI_USE_VERTEXAI=FALSE
GOOGLE_API_KEY=your_api_key_here
````

## File: 9-callbacks/before_after_agent/agent.py
````python
from datetime import datetime
from typing import Optional

from google.adk.agents import LlmAgent
from google.adk.agents.callback_context import CallbackContext
from google.genai import types


def before_agent_callback(callback_context: CallbackContext) -> Optional[types.Content]:


    state = callback_context.state


    timestamp = datetime.now()


    if "agent_name" not in state:
        state["agent_name"] = "SimpleChatBot"


    if "request_counter" not in state:
        state["request_counter"] = 1
    else:
        state["request_counter"] += 1


    state["request_start_time"] = timestamp


    print("=== AGENT EXECUTION STARTED ===")
    print(f"Request #: {state['request_counter']}")
    print(f"Timestamp: {timestamp.strftime('%Y-%m-%d %H:%M:%S')}")


    print(f"\n[BEFORE CALLBACK] Agent processing request #{state['request_counter']}")

    return None


def after_agent_callback(callback_context: CallbackContext) -> Optional[types.Content]:


    state = callback_context.state


    timestamp = datetime.now()
    duration = None
    if "request_start_time" in state:
        duration = (timestamp - state["request_start_time"]).total_seconds()


    print("=== AGENT EXECUTION COMPLETED ===")
    print(f"Request #: {state.get('request_counter', 'Unknown')}")
    if duration is not None:
        print(f"Duration: {duration:.2f} seconds")


    print(
        f"[AFTER CALLBACK] Agent completed request #{state.get('request_counter', 'Unknown')}"
    )
    if duration is not None:
        print(f"[AFTER CALLBACK] Processing took {duration:.2f} seconds")

    return None



root_agent = LlmAgent(
    name="before_after_agent",
    model="gemini-2.0-flash",
    description="A basic agent that demonstrates before and after agent callbacks",
    instruction="""
    You are a friendly greeting agent. Your name is {agent_name}.

    Your job is to:
    - Greet users politely
    - Respond to basic questions
    - Keep your responses friendly and concise
    """,
    before_agent_callback=before_agent_callback,
    after_agent_callback=after_agent_callback,
)
````

## File: 9-callbacks/before_after_model/__init__.py
````python
from . import agent
````

## File: 9-callbacks/before_after_model/.env.example
````
GOOGLE_GENAI_USE_VERTEXAI=FALSE
GOOGLE_API_KEY=your_api_key_here
````

## File: 9-callbacks/before_after_model/agent.py
````python
import copy
from datetime import datetime
from typing import Optional

from google.adk.agents import LlmAgent
from google.adk.agents.callback_context import CallbackContext
from google.adk.models import LlmRequest, LlmResponse
from google.genai import types


def before_model_callback(
    callback_context: CallbackContext, llm_request: LlmRequest
) -> Optional[LlmResponse]:


    state = callback_context.state
    agent_name = callback_context.agent_name


    last_user_message = ""
    if llm_request.contents and len(llm_request.contents) > 0:
        for content in reversed(llm_request.contents):
            if content.role == "user" and content.parts and len(content.parts) > 0:
                if hasattr(content.parts[0], "text") and content.parts[0].text:
                    last_user_message = content.parts[0].text
                    break


    print("=== MODEL REQUEST STARTED ===")
    print(f"Agent: {agent_name}")
    if last_user_message:
        print(f"User message: {last_user_message[:100]}...")

        state["last_user_message"] = last_user_message
    else:
        print("User message: <empty>")

    print(f"Timestamp: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")


    if last_user_message and "sucks" in last_user_message.lower():
        print("=== INAPPROPRIATE CONTENT BLOCKED ===")
        print("Blocked text containing prohibited word: 'sucks'")

        print("[BEFORE MODEL] ⚠️ Request blocked due to inappropriate content")


        return LlmResponse(
            content=types.Content(
                role="model",
                parts=[
                    types.Part(
                        text="I cannot respond to messages containing inappropriate language. "
                        "Please rephrase your request without using words like 'sucks'."
                    )
                ],
            )
        )


    state["model_start_time"] = datetime.now()
    print("[BEFORE MODEL] ✓ Request approved for processing")


    return None


def after_model_callback(
    callback_context: CallbackContext, llm_response: LlmResponse
) -> Optional[LlmResponse]:


    print("[AFTER MODEL] Processing response")


    if not llm_response or not llm_response.content or not llm_response.content.parts:
        return None


    response_text = ""
    for part in llm_response.content.parts:
        if hasattr(part, "text") and part.text:
            response_text += part.text

    if not response_text:
        return None


    replacements = {
        "problem": "challenge",
        "difficult": "complex",
    }


    modified_text = response_text
    modified = False

    for original, replacement in replacements.items():
        if original in modified_text.lower():
            modified_text = modified_text.replace(original, replacement)
            modified_text = modified_text.replace(
                original.capitalize(), replacement.capitalize()
            )
            modified = True


    if modified:
        print("[AFTER MODEL] ↺ Modified response text")

        modified_parts = [copy.deepcopy(part) for part in llm_response.content.parts]
        for i, part in enumerate(modified_parts):
            if hasattr(part, "text") and part.text:
                modified_parts[i].text = modified_text

        return LlmResponse(content=types.Content(role="model", parts=modified_parts))


    return None



root_agent = LlmAgent(
    name="content_filter_agent",
    model="gemini-2.0-flash",
    description="An agent that demonstrates model callbacks for content filtering and logging",
    instruction="""
    You are a helpful assistant.

    Your job is to:
    - Answer user questions concisely
    - Provide factual information
    - Be friendly and respectful
    """,
    before_model_callback=before_model_callback,
    after_model_callback=after_model_callback,
)
````

## File: 9-callbacks/before_after_tool/__init__.py
````python
from . import agent
````

## File: 9-callbacks/before_after_tool/.env.example
````
GOOGLE_GENAI_USE_VERTEXAI=FALSE
GOOGLE_API_KEY=your_api_key_here
````

## File: 9-callbacks/before_after_tool/agent.py
````python
import copy
from typing import Any, Dict, Optional

from google.adk.agents import LlmAgent
from google.adk.tools.base_tool import BaseTool
from google.adk.tools.tool_context import ToolContext



def get_capital_city(country: str) -> Dict[str, str]:

    print(f"[TOOL] Executing get_capital_city tool with country: {country}")

    country_capitals = {
        "united states": "Washington, D.C.",
        "usa": "Washington, D.C.",
        "canada": "Ottawa",
        "france": "Paris",
        "germany": "Berlin",
        "japan": "Tokyo",
        "brazil": "Brasília",
        "australia": "Canberra",
        "india": "New Delhi",
    }


    result = country_capitals.get(country.lower(), f"Capital not found for {country}")
    print(f"[TOOL] Result: {result}")
    print(f"[TOOL] Returning: {{'result': '{result}'}}")

    return {"result": result}



def before_tool_callback(
    tool: BaseTool, args: Dict[str, Any], tool_context: ToolContext
) -> Optional[Dict]:

    tool_name = tool.name
    print(f"[Callback] Before tool call for '{tool_name}'")
    print(f"[Callback] Original args: {args}")


    if tool_name == "get_capital_city" and args.get("country", "").lower() == "merica":
        print("[Callback] Converting 'Merica to 'United States'")
        args["country"] = "United States"
        print(f"[Callback] Modified args: {args}")
        return None

    # Skip the call completely for restricted countries
    if (
        tool_name == "get_capital_city"
        and args.get("country", "").lower() == "restricted"
    ):
        print("[Callback] Blocking restricted country")
        return {"result": "Access to this information has been restricted."}

    print("[Callback] Proceeding with normal tool call")
    return None


# --- Define After Tool Callback ---
def after_tool_callback(
    tool: BaseTool, args: Dict[str, Any], tool_context: ToolContext, tool_response: Dict
) -> Optional[Dict]:

    tool_name = tool.name
    print(f"[Callback] After tool call for '{tool_name}'")
    print(f"[Callback] Args used: {args}")
    print(f"[Callback] Original response: {tool_response}")

    original_result = tool_response.get("result", "")
    print(f"[Callback] Extracted result: '{original_result}'")

    # Add a note for any USA capital responses
    if tool_name == "get_capital_city" and "washington" in original_result.lower():
        print("[Callback] DETECTED USA CAPITAL - adding patriotic note!")

        # Create a modified copy of the response
        modified_response = copy.deepcopy(tool_response)
        modified_response["result"] = (
            f"{original_result} (Note: This is the capital of the USA. 🇺🇸)"
        )
        modified_response["note_added_by_callback"] = True

        print(f"[Callback] Modified response: {modified_response}")
        return modified_response

    print("[Callback] No modifications needed, returning original response")
    return None


# Create the Agent
root_agent = LlmAgent(
    name="tool_callback_agent",
    model="gemini-2.0-flash",
    description="An agent that demonstrates tool callbacks by looking up capital cities",
    instruction="""
    You are a helpful geography assistant.

    Your job is to:
    - Find capital cities when asked using the get_capital_city tool
    - Use the exact country name provided by the user
    - ALWAYS return the EXACT result from the tool, without changing it
    - When reporting a capital, display it EXACTLY as returned by the tool

    Examples:
    - "What is the capital of France?" → Use get_capital_city with country="France"
    - "Tell me the capital city of Japan" → Use get_capital_city with country="Japan"
    """,
    tools=[get_capital_city],
    before_tool_callback=before_tool_callback,
    after_tool_callback=after_tool_callback,
)
````

## File: 9-callbacks/README.md
````markdown
# Callbacks in ADK

This example demonstrates how to use callbacks in the Agent Development Kit (ADK) to intercept and modify agent behavior at different stages of execution. Callbacks provide powerful hooks into the agent's lifecycle, allowing you to add custom logic for monitoring, logging, content filtering, and result transformation.

## What are Callbacks in ADK?

Callbacks are functions that execute at specific points in an agent's execution flow. They allow you to:

1. **Monitor and Log**: Track agent activity and performance metrics
2. **Filter Content**: Block inappropriate requests or responses
3. **Transform Data**: Modify inputs and outputs in the agent workflow
4. **Implement Security Policies**: Enforce compliance and safety measures
5. **Add Custom Logic**: Insert business-specific processing into the agent flow

ADK provides several types of callbacks that can be attached to different components of your agent system.

## Callback Parameters and Context

Each type of callback provides access to specific context objects that contain valuable information about the current execution state. Understanding these parameters is key to building effective callbacks.

### CallbackContext

The `CallbackContext` object is provided to all callback types and contains:

- **`agent_name`**: The name of the agent being executed
- **`invocation_id`**: A unique identifier for the current agent invocation
- **`state`**: Access to the session state, allowing you to read/write persistent data
- **`app_name`**: The name of the application
- **`user_id`**: The ID of the current user
- **`session_id`**: The ID of the current session

Example usage:
```python
def my_callback(callback_context: CallbackContext, ...):
    # Access the state to store or retrieve data
    user_name = callback_context.state.get("user_name", "Unknown")
    
    # Log the current agent and invocation
    print(f"Agent {callback_context.agent_name} executing (ID: {callback_context.invocation_id})")
```

### ToolContext (for Tool Callbacks)

The `ToolContext` object is provided to tool callbacks and contains:

- **`agent_name`**: The name of the agent that initiated the tool call
- **`state`**: Access to the session state, allowing tools to read/modify shared data
- **`properties`**: Additional properties specific to the tool execution

Example usage:
```python
def before_tool_callback(tool: BaseTool, args: Dict[str, Any], tool_context: ToolContext):
    # Record tool usage in state
    tools_used = tool_context.state.get("tools_used", [])
    tools_used.append(tool.name)
    tool_context.state["tools_used"] = tools_used
```

### LlmRequest (for Model Callbacks)

The `LlmRequest` object is provided to the before_model_callback and contains:

- **`contents`**: List of Content objects representing the conversation history
- **`generation_config`**: Configuration for the model generation
- **`safety_settings`**: Safety settings for the model
- **`tools`**: Tools provided to the model

Example usage:
```python
def before_model_callback(callback_context: CallbackContext, llm_request: LlmRequest):
    # Get the last user message for analysis
    last_message = None
    for content in reversed(llm_request.contents):
        if content.role == "user" and content.parts:
            last_message = content.parts[0].text
            break
            
    # Analyze the user's message
    if last_message and contains_sensitive_info(last_message):
        # Return a response that bypasses the model call
        return LlmResponse(...)
```

### LlmResponse (for Model Callbacks)

The `LlmResponse` object is returned from the model and provided to the after_model_callback:

- **`content`**: Content object containing the model's response
- **`tool_calls`**: Any tool calls the model wants to make
- **`usage_metadata`**: Metadata about the model usage (tokens, etc.)

Example usage:
```python
def after_model_callback(callback_context: CallbackContext, llm_response: LlmResponse):
    # Access the model's text response
    if llm_response.content and llm_response.content.parts:
        response_text = llm_response.content.parts[0].text
        
        # Modify the response
        modified_text = transform_text(response_text)
        llm_response.content.parts[0].text = modified_text
        
        return llm_response
```

## Types of Callbacks Demonstrated

This project includes three examples of callback patterns:

### 1. Agent Callbacks (`before_after_agent/`)
- **Before Agent Callback**: Runs at the start of agent processing
- **After Agent Callback**: Runs after the agent completes processing

### 2. Model Callbacks (`before_after_model/`)
- **Before Model Callback**: Intercepts requests before they reach the LLM
- **After Model Callback**: Modifies responses after they come from the LLM

### 3. Tool Callbacks (`before_after_tool/`)
- **Before Tool Callback**: Modifies tool arguments or skips tool execution
- **After Tool Callback**: Enhances tool responses with additional information

## Project Structure

```
8-callbacks/
│
├── before_after_agent/           # Agent callback example
│   ├── __init__.py               # Required for ADK discovery
│   ├── agent.py                  # Agent with agent callbacks
│   └── .env                      # Environment variables
│
├── before_after_model/           # Model callback example
│   ├── __init__.py               # Required for ADK discovery
│   ├── agent.py                  # Agent with model callbacks
│   └── .env                      # Environment variables
│
├── before_after_tool/            # Tool callback example
│   ├── __init__.py               # Required for ADK discovery
│   ├── agent.py                  # Agent with tool callbacks
│   └── .env                      # Environment variables
│
└── README.md                     # This documentation
```

## Example 1: Agent Callbacks

The agent callbacks example demonstrates:

1. **Request Logging**: Recording when requests start and finish
2. **Performance Monitoring**: Measuring request duration
3. **State Management**: Using session state to track request counts

### Key Implementation Details

```python
def before_agent_callback(callback_context: CallbackContext) -> Optional[types.Content]:
    # Get the session state
    state = callback_context.state
    
    # Initialize request counter
    if "request_counter" not in state:
        state["request_counter"] = 1
    else:
        state["request_counter"] += 1
        
    # Store start time for duration calculation
    state["request_start_time"] = datetime.now()
    
    # Log the request
    logger.info("=== AGENT EXECUTION STARTED ===")
    
    return None  # Continue with normal agent processing

def after_agent_callback(callback_context: CallbackContext) -> Optional[types.Content]:
    # Get the session state
    state = callback_context.state
    
    # Calculate request duration
    duration = None
    if "request_start_time" in state:
        duration = (datetime.now() - state["request_start_time"]).total_seconds()
        
    # Log the completion
    logger.info("=== AGENT EXECUTION COMPLETED ===")
    
    return None  # Continue with normal agent processing
```

### Testing Agent Callbacks

Any interaction will demonstrate the agent callbacks, which log requests and measure duration.

## Example 2: Model Callbacks

The model callbacks example demonstrates:

1. **Content Filtering**: Blocking inappropriate content before it reaches the model
2. **Response Transformation**: Replacing negative words with more positive alternatives

### Key Implementation Details

```python
def before_model_callback(
    callback_context: CallbackContext, llm_request: LlmRequest
) -> Optional[LlmResponse]:
    # Check for inappropriate content
    if last_user_message and "sucks" in last_user_message.lower():
        # Return a response to skip the model call
        return LlmResponse(
            content=types.Content(
                role="model",
                parts=[
                    types.Part(
                        text="I cannot respond to messages containing inappropriate language..."
                    )
                ],
            )
        )
    # Return None to proceed with normal model request
    return None

def after_model_callback(
    callback_context: CallbackContext, llm_response: LlmResponse
) -> Optional[LlmResponse]:
    # Simple word replacements
    replacements = {
        "problem": "challenge",
        "difficult": "complex",
    }
    # Perform replacements and return modified response
```

### Testing Model Callbacks

To test content filtering in the before_model_callback:
- "This website sucks, can you help me fix it?"
- "Everything about this project sucks."

To test word replacement in the after_model_callback:
- "What's the biggest problem with machine learning today?"
- "Why is debugging so difficult in complex systems?"
- "I have a problem with my code that's very difficult to solve."

## Example 3: Tool Callbacks

The tool callbacks example demonstrates:

1. **Argument Modification**: Transforming input arguments before tool execution
2. **Request Blocking**: Preventing certain tool calls completely
3. **Response Enhancement**: Adding additional context to tool responses
4. **Error Handling**: Improving error messages for better user experience

### Key Implementation Details

```python
def before_tool_callback(
    tool: BaseTool, args: Dict[str, Any], tool_context: ToolContext
) -> Optional[Dict]:
    # Modify arguments (e.g., convert "USA" to "United States")
    if args.get("country", "").lower() == "merica":
        args["country"] = "United States"
        return None
        
    # Skip the call completely for restricted countries
    if args.get("country", "").lower() == "restricted":
        return {"result": "Access to this information has been restricted."}
    
    return None  # Proceed with normal tool call

def after_tool_callback(
    tool: BaseTool, args: Dict[str, Any], tool_context: ToolContext, tool_response: Dict
) -> Optional[Dict]:
    # Add a note for any USA capital responses
    if "washington" in tool_response.get("result", "").lower():
        modified_response = copy.deepcopy(tool_response)
        modified_response["result"] = f"{tool_response['result']} (Note: This is the capital of the USA. 🇺🇸)"
        return modified_response
        
    return None  # Use original response
```

### Testing Tool Callbacks

To test argument modification:
- "What is the capital of USA?" (converts to "United States")
- "What is the capital of Merica?" (converts to "United States")

To test request blocking:
- "What is the capital of restricted?" (blocks the request)

To test response enhancement:
- "What is the capital of the United States?" (adds a patriotic note)

To see normal operation:
- "What is the capital of France?" (no modifications)

## Running the Examples

### Setup

1. Activate the virtual environment from the root directory:
```bash
# macOS/Linux:
source ../.venv/bin/activate
# Windows CMD:
..\.venv\Scripts\activate.bat
# Windows PowerShell:
..\.venv\Scripts\Activate.ps1
```

2. Create a `.env` file in each agent directory (`before_after_agent/`, `before_after_model/`, and `before_after_tool/`) based on the provided `.env.example` files:
```
GOOGLE_API_KEY=your_api_key_here
```

### Running the Examples

```bash
cd 8-callbacks
adk web
```

Then select the agent you want to test from the dropdown menu in the web UI:
- "before_after_agent" to test agent callbacks
- "before_after_model" to test model callbacks
- "before_after_tool" to test tool callbacks

## Additional Resources

- [ADK Callbacks Documentation](https://google.github.io/adk-docs/callbacks/)
- [Types of Callbacks](https://google.github.io/adk-docs/callbacks/types-of-callbacks/)
- [Design Patterns and Best Practices](https://google.github.io/adk-docs/callbacks/design-patterns-and-best-practices/)
````

## File: .gitignore
````
.env
__pycache__/
.venv/
*.db
````

## File: GEMINI_MODELS.md
````markdown
# Gemini Model Overview for ADK

ADK supports several Gemini models with different capabilities and price points. Choosing the right model involves balancing performance, capabilities, and cost for your specific use case.

## Model Capabilities

| Model | Description | Input Types | Best For |
|-------|-------------|-------------|----------|
| gemini-2.5-pro | Most powerful thinking model with maximum response accuracy | Audio, images, video, text | Complex coding, reasoning, multimodal understanding |
| gemini-2.5-flash | Best price-performance balance | Audio, images, video, text | Low latency, high volume tasks that require thinking |
| gemini-2.0-flash | Newest multimodal model with improved capabilities | Audio, images, video, text | Low latency, enhanced performance, agentic experiences |
| gemini-2.0-flash-lite | Optimized for efficiency and speed | Audio, images, video, text | Cost efficiency and low latency |
| gemini-1.5-flash | Versatile performance across diverse tasks | Audio, images, video, text | Fast and versatile performance |
| gemini-1.5-flash-8b | Smaller, faster model | Audio, images, video, text | High volume and lower intelligence tasks |
| gemini-1.5-pro | Powerful reasoning capabilities | Audio, images, video, text | Complex reasoning tasks requiring more intelligence |

## Pricing

| Model | Input Price | Output Price |
|-------|-------------|-------------|
| gemini-2.5-pro | $10.00 / 1M tokens | $30.00 / 1M tokens |
| gemini-2.5-flash | $3.50 / 1M tokens | $10.50 / 1M tokens |
| gemini-2.0-flash | $3.50 / 1M tokens | $10.50 / 1M tokens |
| gemini-2.0-flash-lite | $0.70 / 1M tokens | $2.10 / 1M tokens |
| gemini-1.5-flash | $2.50 / 1M tokens | $7.50 / 1M tokens |
| gemini-1.5-flash-8b | $0.35 / 1M tokens | $1.05 / 1M tokens |
| gemini-1.5-pro | $7.00 / 1M tokens | $21.00 / 1M tokens |

## Token Information

- A token is approximately 4 characters
- 100 tokens are roughly 60-80 English words
- Pricing is calculated based on both input tokens (prompts sent to the model) and output tokens (responses generated by the model)

## Model Selection Guidelines

1. **For budget-conscious applications:** Start with gemini-2.0-flash-lite
2. **For balanced performance and cost:** Use gemini-2.0-flash or gemini-2.5-flash
3. **For complex reasoning tasks:** Choose gemini-2.5-pro
4. **For production applications:** Prefer stable models over experimental/preview versions

## Additional Resources

For the most up-to-date information on Gemini models, visit the [official Gemini API documentation](https://ai.google.dev/gemini-api/docs/models).
````

## File: README.md
````markdown
# Agent Development Kit (ADK) Crash Course

This repository contains examples for learning Google's Agent Development Kit (ADK), a powerful framework for building LLM-powered agents.

## Getting Started

### Setup Environment

You only need to create one virtual environment for all examples in this course. Follow these steps to set it up:

```bash
# Create virtual environment in the root directory
python -m venv .venv

# Activate (each new terminal)
# macOS/Linux:
source .venv/bin/activate
# Windows CMD:
.venv\Scripts\activate.bat
# Windows PowerShell:
.venv\Scripts\Activate.ps1

# Install dependencies
pip install -r requirements.txt
```

Once set up, this single environment will work for all examples in the repository.

### Setting Up API Keys

1. Create an account in Google Cloud https://cloud.google.com/?hl=en
2. Create a new project
3. Go to https://aistudio.google.com/apikey
4. Create an API key
5. Assign key to the project
6. Connect to a billing account

Each example folder contains a `.env.example` file. For each project you want to run:

1. Navigate to the example folder
2. Rename `.env.example` to `.env` 
3. Open the `.env` file and replace the placeholder with your API key:
   ```
   GOOGLE_API_KEY=your_api_key_here
   ```

You'll need to repeat this for each example project you want to run.

## Examples Overview

Here's what you can learn from each example folder:

### 1. Basic Agent
Introduction to the simplest form of ADK agents. Learn how to create a basic agent that can respond to user queries.

### 2. Tool Agent
Learn how to enhance agents with tools that allow them to perform actions beyond just generating text.

### 3. LiteLLM Agent
Example of using LiteLLM to abstract away LLM provider details and easily switch between different models.

### 4. Structured Outputs
Learn how to use Pydantic models with `output_schema` to ensure consistent, structured responses from your agents.

### 5. Sessions and State
Understand how to maintain state and memory across multiple interactions using sessions.

### 6. Persistent Storage
Learn techniques for storing agent data persistently across sessions and application restarts.

### 7. Multi-Agent
See how to orchestrate multiple specialized agents working together to solve complex tasks.

### 8. Stateful Multi-Agent
Build agents that maintain and update state throughout complex multi-turn conversations.

### 9. Callbacks
Implement event callbacks to monitor and respond to agent behaviors in real-time.

### 10. Sequential Agent
Create pipeline workflows where agents operate in a defined sequence to process information.

### 11. Parallel Agent
Leverage concurrent operations with parallel agents for improved efficiency and performance.

### 12. Loop Agent
Build sophisticated agents that can iteratively refine their outputs through feedback loops.

## Official Documentation

For more detailed information, check out the official ADK documentation:
- https://google.github.io/adk-docs/get-started/quickstart

## Support

Need help or run into issues? Join our free AI Developer Accelerator community on Skool:
- [AI Developer Accelerator Community](https://www.skool.com/ai-developer-accelerator/about)

In the community you'll find:
- Weekly coaching and support calls
- Early access to code from YouTube projects
- A network of AI developers of all skill levels ready to help
- Behind-the-scenes looks at how these apps are built
````

## File: requirements.txt
````
google-adk[database]==0.3.0
yfinance==0.2.56
psutil==5.9.5
litellm==1.66.3
google-generativeai==0.8.5
python-dotenv==1.1.0
````
