[{"name": "Spaghetti och köttfärssås", "synonyms": ["Spaghetti bolognese", "Pasta bolognese", "Köttfärssås med pasta", "Spaghetti med köttfärssås"], "preferences": {"A": 67, "B": 78, "C": 45, "D": 62}, "types": ["meal"]}, {"name": "Köttbullar med potatismos", "synonyms": ["Svenska köttbullar", "<PERSON><PERSON><PERSON><PERSON><PERSON>"], "preferences": {"A": 80, "B": 80, "C": 25, "D": 85}, "types": ["meal"]}, {"name": "Pannkakor", "synonyms": ["Svenska pannkakor", "Pannkaksrätt"], "preferences": {"A": 70, "B": 70, "C": 70, "D": 75}, "types": ["meal"]}, {"name": "<PERSON><PERSON>v med stuvade makaroner", "synonyms": ["Falukorv med stuvade makaroner", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON> med makaroner"], "preferences": {"A": 75, "B": 75, "C": 30, "D": 80}, "types": ["meal"]}, {"name": "Stekt falukorv", "synonyms": ["Falukorv", "Stekt korv", "Falukorvsstekning"], "preferences": {"A": 70, "B": 70, "C": 20, "D": 85}, "types": ["module", "protein"]}, {"name": "<PERSON><PERSON><PERSON> ma<PERSON>", "synonyms": ["Makaroner i stuvning", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], "preferences": {"A": 70, "B": 70, "C": 50, "D": 80}, "types": ["module", "side"]}, {"name": "Raggmunk med fläsk", "synonyms": ["<PERSON><PERSON><PERSON><PERSON>", "Potatisplättar med fläsk"], "preferences": {"A": 75, "B": 75, "C": 30, "D": 80}, "types": ["meal"]}, {"name": "Grillad korv med bröd", "synonyms": ["<PERSON><PERSON><PERSON> med bröd"], "preferences": {"A": 75, "B": 75, "C": 35, "D": 85}, "types": ["meal"]}, {"name": "Tacos", "synonyms": ["Mexikanska tacos", "Kötttacos", "Tacos med köttfärs", "Tacos med kyckling", "Tex-mex tacos"], "preferences": {"A": 90, "B": 80, "C": 80, "D": 90}, "types": ["meal"]}, {"name": "Kebabpizza", "synonyms": ["Pizza med kebab", "Kebab och pizza"], "preferences": {"A": 45, "B": 60, "C": 30, "D": 60}, "types": ["meal"]}, {"name": "Pizza", "synonyms": ["Pizza margarita", "Pizza med pesto", "Pizza med köttfärs", "Pizza med kyckling"], "preferences": {"A": 75, "B": 75, "C": 40, "D": 80}, "types": ["meal"]}, {"name": "Fiskgratäng", "synonyms": ["Gratinerad fisk", "Fisk i gratäng"], "preferences": {"A": 70, "B": 15, "C": 10, "D": 65}, "types": ["meal"]}, {"name": "Ärtsoppa med pannkakor", "synonyms": ["<PERSON><PERSON><PERSON><PERSON>", "Ärtsoppa med pannkakor"], "preferences": {"A": 75, "B": 75, "C": 70, "D": 80}, "types": ["meal"]}, {"name": "<PERSON><PERSON><PERSON>", "synonyms": ["<PERSON><PERSON><PERSON>ff med korv"], "preferences": {"A": 80, "B": 80, "C": 20, "D": 85}, "types": ["meal"]}, {"name": "Stekt fläsk med löksås", "synonyms": ["Fläsk med löksås", "Stekt fläsk med sås"], "preferences": {"A": 80, "B": 80, "C": 25, "D": 85}, "types": ["meal"]}, {"name": "Kåldolmar", "synonyms": ["<PERSON><PERSON><PERSON><PERSON><PERSON> med kött", "Kåldolmar"], "preferences": {"A": 75, "B": 75, "C": 40, "D": 80}, "types": ["meal"]}, {"name": "Potatisgratäng", "synonyms": ["Gratinerad potatis", "Potatis i gratäng"], "preferences": {"A": 70, "B": 70, "C": 60, "D": 70}, "types": ["meal"]}, {"name": "Ren<PERSON>v", "synonyms": ["Ren<PERSON>v", "Renskavs"], "preferences": {"A": 70, "B": 70, "C": 30, "D": 75}, "types": ["meal"]}, {"name": "<PERSON><PERSON>", "synonyms": ["<PERSON><PERSON><PERSON><PERSON><PERSON>", "Biff med potatis"], "preferences": {"A": 80, "B": 80, "C": 20, "D": 85}, "types": ["meal"]}, {"name": "Toast Skagen", "synonyms": ["Skagenröra på toast", "Skagen toast"], "preferences": {"A": 70, "B": 10, "C": 5, "D": 65}, "types": ["meal"]}, {"name": "Lax med dillstuvad potatis", "synonyms": ["Lax med dillpotatis", "Dillstuvad potatis med lax"], "preferences": {"A": 70, "B": 10, "C": 5, "D": 60}, "types": ["meal"]}, {"name": "Kroppkakor", "synonyms": ["Potatiskroppkakor", "Kroppkakor med fläsk"], "preferences": {"A": 75, "B": 75, "C": 35, "D": 80}, "types": ["meal"]}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "synonyms": ["<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "preferences": {"A": 70, "B": 70, "C": 40, "D": 75}, "types": ["meal"]}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "synonyms": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "preferences": {"A": 75, "B": 75, "C": 70, "D": 80}, "types": ["meal"]}, {"name": "<PERSON><PERSON><PERSON> ky<PERSON> med rotfrukter", "synonyms": ["Ugnsrostad kyckling med rotfrukter", "Kyckling med ugnsrostade grönsaker"], "preferences": {"A": 80, "B": 80, "C": 30, "D": 85}, "types": ["meal"]}, {"name": "Grönsakssoppa", "synonyms": ["Soppa på grönsaker", "Grönsakssoppa"], "preferences": {"A": 70, "B": 70, "C": 70, "D": 75}, "types": ["meal"]}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "synonyms": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON> p<PERSON>"], "preferences": {"A": 75, "B": 75, "C": 35, "D": 80}, "types": ["meal"]}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "synonyms": ["Inlagd sill med tillbehör", "<PERSON><PERSON><PERSON><PERSON><PERSON>"], "preferences": {"A": 70, "B": 10, "C": 5, "D": 65}, "types": ["meal"]}, {"name": "<PERSON>ag<PERSON>", "synonyms": ["Pastalåda", "Italiensk lasagne"], "preferences": {"A": 80, "B": 80, "C": 25, "D": 95}, "types": ["meal"]}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "synonyms": ["Mexikanska enchiladas", "<PERSON><PERSON><PERSON> tortillas"], "preferences": {"A": 80, "B": 80, "C": 30, "D": 95}, "types": ["meal"]}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "synonyms": ["Grönsaksgry<PERSON>", "Fransk ratatouille"], "preferences": {"A": 75, "B": 75, "C": 60, "D": 80}, "types": ["meal", "module", "sauce"]}, {"name": "Shakshu<PERSON>", "synonyms": ["Ägg i tomatsås", "Shakshu<PERSON>"], "preferences": {"A": 75, "B": 75, "C": 50, "D": 80}, "types": ["meal"]}, {"name": "Quesadillas", "synonyms": ["Ostquesadillas", "Mexikanska quesadillas"], "preferences": {"A": 80, "B": 80, "C": 40, "D": 85}, "types": ["meal"]}, {"name": "<PERSON><PERSON><PERSON> bönröra", "synonyms": ["Refried beans", "<PERSON><PERSON><PERSON>"], "preferences": {"A": 70, "B": 70, "C": 65, "D": 80}, "types": ["module"]}, {"name": "Guacamole", "synonyms": ["Avokadodipp", "Avokadosås"], "preferences": {"A": 70, "B": 70, "C": 50, "D": 90}, "types": ["module", "sauce"]}, {"name": "Pasta Pomodoro med Mozarella", "synonyms": ["Pomodoro med mozzarella", "Pasta med tomatsås och mozzarella"], "preferences": {"A": 80, "B": 80, "C": 30, "D": 95}, "types": ["meal"]}, {"name": "Fetaostkräm", "synonyms": ["Fetaostdipp", "Krämig fetaost"], "preferences": {"A": 70, "B": 70, "C": 40, "D": 90}, "types": ["module", "sauce"]}, {"name": "Mozarella", "synonyms": ["Mozzarellaost", "Färsk mozzarella"], "preferences": {"A": 70, "B": 70, "C": 50, "D": 90}, "types": ["module", "topping"]}, {"name": "Gravad lax med hovmästarsås", "synonyms": ["Gravad lax", "Lax med hovmästarsås"], "preferences": {"A": 70, "B": 10, "C": 5, "D": 60}, "types": ["meal"]}, {"name": "Kallrökt lax med dill", "synonyms": ["Kallrökt lax", "<PERSON><PERSON><PERSON> lax med dill"], "preferences": {"A": 70, "B": 10, "C": 5, "D": 60}, "types": ["meal"]}, {"name": "Gravad lax med senapssås", "synonyms": ["Lax med senapssås", "Gravad lax med senap"], "preferences": {"A": 70, "B": 10, "C": 5, "D": 60}, "types": ["meal"]}, {"name": "Rödbetssallad", "synonyms": ["<PERSON><PERSON> på röd<PERSON>or", "Rödbetssallad"], "preferences": {"A": 70, "B": 70, "C": 70, "D": 75}, "types": ["module"]}, {"name": "Varmkorv med bröd", "synonyms": ["<PERSON><PERSON><PERSON> med bröd"], "preferences": {"A": 75, "B": 75, "C": 40, "D": 85}, "types": ["meal"]}, {"name": "<PERSON><PERSON><PERSON>", "synonyms": ["<PERSON><PERSON><PERSON>", "Svenska bruna bönor"], "preferences": {"A": 70, "B": 70, "C": 60, "D": 75}, "types": ["module", "legume"]}, {"name": "<PERSON>ad potatis", "synonyms": ["Potatismos", "<PERSON>ad potatis"], "preferences": {"A": 70, "B": 70, "C": 65, "D": 75}, "types": ["module"]}, {"name": "Gräddfilssås", "synonyms": ["<PERSON><PERSON><PERSON><PERSON>", "Gräddfilssås"], "preferences": {"A": 70, "B": 70, "C": 20, "D": 85}, "types": ["module", "sauce"]}, {"name": "Grönsallad", "synonyms": ["<PERSON><PERSON>", "Grönsaksallad"], "preferences": {"A": 70, "B": 70, "C": 70, "D": 75}, "types": ["module", "vegetable"]}, {"name": "<PERSON><PERSON><PERSON> ky<PERSON>", "synonyms": ["<PERSON><PERSON><PERSON> ky<PERSON>", "<PERSON><PERSON><PERSON> k<PERSON>"], "preferences": {"A": 80, "B": 80, "C": 25, "D": 90}, "types": ["meal"]}, {"name": "Biff med lök", "synonyms": ["<PERSON><PERSON><PERSON><PERSON><PERSON>", "Biff med stekt lök"], "preferences": {"A": 80, "B": 80, "C": 20, "D": 85}, "types": ["meal"]}, {"name": "<PERSON><PERSON>", "synonyms": ["Svensk kalops", "<PERSON><PERSON>"], "preferences": {"A": 75, "B": 75, "C": 30, "D": 80}, "types": ["meal"]}, {"name": "Renskavsgryta", "synonyms": ["Renskavsgryta", "Renskavsgryta"], "preferences": {"A": 70, "B": 70, "C": 30, "D": 80}, "types": ["meal"]}, {"name": "Stekt fläsk", "synonyms": ["Stekt fläsk", "Fläskstek"], "preferences": {"A": 80, "B": 80, "C": 30, "D": 85}, "types": ["meal"]}, {"name": "Fläskpannkaka", "synonyms": ["Pannkaka med fläsk", "Fläskpannkaka"], "preferences": {"A": 75, "B": 75, "C": 35, "D": 80}, "types": ["meal"]}, {"name": "Ärtsoppa med fläsk", "synonyms": ["Ärtsoppa med fläsk", "Fläskig ärtsoppa"], "preferences": {"A": 75, "B": 75, "C": 65, "D": 80}, "types": ["meal"]}, {"name": "Varm räkcocktail", "synonyms": ["<PERSON><PERSON><PERSON><PERSON><PERSON>", "Varm räkcocktail"], "preferences": {"A": 70, "B": 10, "C": 5, "D": 65}, "types": ["meal"]}, {"name": "Skagenröra", "synonyms": ["Skagenröra", "Skagen dressing"], "preferences": {"A": 70, "B": 10, "C": 5, "D": 65}, "types": ["module"]}, {"name": "Räksmörgås", "synonyms": ["Smörgås med räkor", "Räksmörgås"], "preferences": {"A": 70, "B": 10, "C": 5, "D": 65}, "types": ["meal"]}, {"name": "Fiskpudding", "synonyms": ["Fiskpudding", "Pudding med fisk"], "preferences": {"A": 70, "B": 15, "C": 5, "D": 65}, "types": ["meal"]}, {"name": "Torsk med äggsås", "synonyms": ["Torsk med äggsås", "Äggs<PERSON><PERSON> och torsk"], "preferences": {"A": 70, "B": 15, "C": 5, "D": 60}, "types": ["meal"]}, {"name": "Musslor i vitvinssås", "synonyms": ["Musslor i sås", "Musslor med vitvinssås"], "preferences": {"A": 70, "B": 10, "C": 5, "D": 60}, "types": ["meal"]}, {"name": "Kräftsoppa", "synonyms": ["Kräftsoppa", "Svensk kräftsoppa"], "preferences": {"A": 70, "B": 10, "C": 5, "D": 60}, "types": ["meal"]}, {"name": "<PERSON><PERSON>", "synonyms": ["Potatissallad", "<PERSON>ll sallad med potatis"], "preferences": {"A": 70, "B": 70, "C": 65, "D": 75}, "types": ["module", "side", "vegetable"]}, {"name": "Varm potatissallad", "synonyms": ["Potat<PERSON><PERSON><PERSON> varm", "Varm sallad med potatis"], "preferences": {"A": 70, "B": 70, "C": 65, "D": 75}, "types": ["module", "side", "vegetable"]}, {"name": "Rödkålssallad", "synonyms": ["<PERSON><PERSON> på rödk<PERSON>l", "Rödkålssallad"], "preferences": {"A": 70, "B": 70, "C": 70, "D": 75}, "types": ["module", "side", "vegetable"]}, {"name": "Stekt potatis", "synonyms": ["Stekt potatis", "<PERSON><PERSON><PERSON> potatis"], "preferences": {"A": 70, "B": 70, "C": 65, "D": 75}, "types": ["module", "side", "vegetable"]}, {"name": "Gräddså<PERSON>", "synonyms": ["<PERSON><PERSON>s med grädde", "Gräddså<PERSON>"], "preferences": {"A": 70, "B": 70, "C": 20, "D": 85}, "types": ["module", "sauce"]}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "synonyms": ["<PERSON><PERSON><PERSON><PERSON>", "Salladssås med dill"], "preferences": {"A": 70, "B": 70, "C": 20, "D": 85}, "types": ["module", "sauce"]}, {"name": "Bearnaisesås", "synonyms": ["Béarnaise", "Bearnaise dressing", "Klassisk bearnaise"], "preferences": {"A": 70, "B": 70, "C": 20, "D": 90}, "types": ["module", "sauce"]}, {"name": "Vitvinssås", "synonyms": ["Vitvinssås", "Sås med vitvin"], "preferences": {"A": 70, "B": 70, "C": 20, "D": 85}, "types": ["module", "sauce"]}, {"name": "Laxsås", "synonyms": ["Sås till lax", "Laxdressing"], "preferences": {"A": 70, "B": 15, "C": 10, "D": 80}, "types": ["module", "sauce"]}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "synonyms": ["<PERSON><PERSON>", "Kryddstark tomatsås"], "preferences": {"A": 70, "B": 70, "C": 20, "D": 85}, "types": ["module", "sauce"]}, {"name": "Pesto", "synonyms": ["<PERSON><PERSON><PERSON><PERSON> pesto", "Basilika<PERSON><PERSON>"], "preferences": {"A": 70, "B": 70, "C": 50, "D": 85}, "types": ["module", "sauce"]}, {"name": "Klassisk svampsås", "synonyms": ["Svampsås", "<PERSON><PERSON><PERSON><PERSON><PERSON>"], "preferences": {"A": 70, "B": 70, "C": 20, "D": 85}, "types": ["module", "sauce"]}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "synonyms": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>"], "preferences": {"A": 70, "B": 70, "C": 20, "D": 85}, "types": ["module", "sauce"]}, {"name": "Majssallad", "synonyms": ["<PERSON>lad med majs", "Majssallad"], "preferences": {"A": 70, "B": 70, "C": 70, "D": 75}, "types": ["module", "side", "vegetable"]}, {"name": "Bönsallad", "synonyms": ["<PERSON><PERSON> med b<PERSON>nor", "Bönsallad"], "preferences": {"A": 70, "B": 70, "C": 70, "D": 75}, "types": ["module", "side", "vegetable"]}, {"name": "Grönsaksmix", "synonyms": ["<PERSON><PERSON><PERSON>", "Grönsaksmix"], "preferences": {"A": 70, "B": 70, "C": 70, "D": 75}, "types": ["module", "side", "vegetable"]}, {"name": "Risotto med svamp", "synonyms": ["Svamprisotto", "Risotto med champinjoner"], "preferences": {"A": 75, "B": 75, "C": 30, "D": 85}, "types": ["meal"]}, {"name": "Risotto med kyckling", "synonyms": ["<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Risotto med kycklingbitar"], "preferences": {"A": 80, "B": 80, "C": 25, "D": 90}, "types": ["meal"]}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "synonyms": ["<PERSON><PERSON>", "<PERSON><PERSON>"], "preferences": {"A": 70, "B": 10, "C": 5, "D": 60}, "types": ["meal"]}, {"name": "Tempura gr<PERSON><PERSON>aker", "synonyms": ["Friterade grönsaker", "Tempuragrönsaker"], "preferences": {"A": 70, "B": 70, "C": 70, "D": 80}, "types": ["meal"]}, {"name": "Pad Thai", "synonyms": ["Thailändsk nudelrätt", "Pad Thai"], "preferences": {"A": 75, "B": 75, "C": 40, "D": 85}, "types": ["meal"]}, {"name": "Nudelsallad med jordnötssås", "synonyms": ["Jordnötss<PERSON><PERSON>", "Nudelsallad med jordnötssmak"], "preferences": {"A": 70, "B": 70, "C": 20, "D": 90}, "types": ["meal"]}, {"name": "Friterade räkor", "synonyms": ["Krispiga räkor", "Friterade skaldjur"], "preferences": {"A": 70, "B": 10, "C": 5, "D": 65}, "types": ["meal"]}, {"name": "Falafel", "synonyms": ["Vegetariska biffar", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Falafelbollar"], "preferences": {"A": 70, "B": 70, "C": 70, "D": 80}, "types": ["meal", "vegetarian"]}, {"name": "<PERSON><PERSON><PERSON>", "synonyms": ["Kikärtssås", "<PERSON><PERSON><PERSON><PERSON><PERSON>"], "preferences": {"A": 70, "B": 70, "C": 40, "D": 85}, "types": ["module", "sauce"]}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "synonyms": ["Libanesisk sallad", "<PERSON><PERSON><PERSON><PERSON><PERSON>"], "preferences": {"A": 70, "B": 70, "C": 70, "D": 75}, "types": ["module"]}, {"name": "<PERSON><PERSON><PERSON>", "synonyms": ["<PERSON><PERSON><PERSON><PERSON> fyllda med kött", "<PERSON><PERSON><PERSON>"], "preferences": {"A": 75, "B": 75, "C": 50, "D": 80}, "types": ["meal"]}, {"name": "Vegetarisk lasagne", "synonyms": ["<PERSON><PERSON><PERSON> vegetarisk", "<PERSON><PERSON><PERSON><PERSON>"], "preferences": {"A": 80, "B": 80, "C": 70, "D": 90}, "types": ["meal", "vegetarian"]}, {"name": "Quinoa<PERSON><PERSON>", "synonyms": ["Quinoa<PERSON><PERSON>", "Quinoablandad sallad"], "preferences": {"A": 70, "B": 70, "C": 70, "D": 75}, "types": ["module"]}, {"name": "Sötpotatispommes", "synonyms": ["Pommes av sötpotatis", "Sötpotatisfries"], "preferences": {"A": 70, "B": 70, "C": 70, "D": 80}, "types": ["module"]}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "synonyms": ["Soppa på pumpa", "<PERSON><PERSON><PERSON><PERSON><PERSON>"], "preferences": {"A": 70, "B": 70, "C": 50, "D": 80}, "types": ["meal"]}, {"name": "Mo<PERSON><PERSON><PERSON>", "synonyms": ["Soppa på morötter", "Mo<PERSON><PERSON><PERSON>"], "preferences": {"A": 70, "B": 70, "C": 50, "D": 80}, "types": ["meal"]}, {"name": "Linsg<PERSON><PERSON>", "synonyms": ["Gryta med linser", "Linsg<PERSON><PERSON>"], "preferences": {"A": 75, "B": 75, "C": 70, "D": 85}, "types": ["meal"]}, {"name": "<PERSON><PERSON> con carne", "synonyms": ["<PERSON><PERSON>", "<PERSON><PERSON> med k<PERSON>tt"], "preferences": {"A": 80, "B": 80, "C": 20, "D": 85}, "types": ["meal"]}, {"name": "Bolognese", "synonyms": ["Köttfärssås", "Bolognese"], "preferences": {"A": 80, "B": 80, "C": 20, "D": 85}, "types": ["meal"]}, {"name": "Carbonara", "synonyms": ["Spaghetti Carbonara", "Pasta Carbonara"], "preferences": {"A": 80, "B": 80, "C": 20, "D": 90}, "types": ["meal"]}, {"name": "<PERSON><PERSON><PERSON><PERSON> med smör", "synonyms": ["Smörgås med smör", "<PERSON><PERSON><PERSON><PERSON> och smör"], "preferences": {"A": 80, "B": 80, "C": 60, "D": 75}, "types": ["module", "side"]}, {"name": "Grillad majs", "synonyms": ["<PERSON><PERSON> grill", "Grillad majs"], "preferences": {"A": 85, "B": 85, "C": 70, "D": 80}, "types": ["module", "side", "vegetable"]}, {"name": "Grilla<PERSON> entrecote", "synonyms": ["Entrecote", "Grillad biff"], "preferences": {"A": 80, "B": 80, "C": 20, "D": 85}, "types": ["meal"]}, {"name": "Oxfilé med potatisgratäng", "synonyms": ["Oxfilé med gratinerad potatis", "Oxfilé med potatis"], "preferences": {"A": 80, "B": 80, "C": 20, "D": 85}, "types": ["meal"]}, {"name": "Fläskfilé med gräddsås", "synonyms": ["Fläskfilé med sås", "Gräddsås med fläskfilé"], "preferences": {"A": 80, "B": 80, "C": 20, "D": 85}, "types": ["meal"]}, {"name": "Lammstek med rosmarin", "synonyms": ["Lammstek", "Rosmarinlammstek"], "preferences": {"A": 75, "B": 75, "C": 30, "D": 85}, "types": ["meal"]}, {"name": "Kycklingwok", "synonyms": ["Wok med kyckling", "Kycklingwok"], "preferences": {"A": 80, "B": 80, "C": 25, "D": 90}, "types": ["meal"]}, {"name": "Fiskgryta", "synonyms": ["Gryta med fisk", "Fiskgryta"], "preferences": {"A": 70, "B": 15, "C": 10, "D": 65}, "types": ["meal"]}, {"name": "Torskrygg med persiljesås", "synonyms": ["Torsk<PERSON>gg", "Torsk med persiljesås"], "preferences": {"A": 70, "B": 15, "C": 10, "D": 65}, "types": ["meal"]}, {"name": "Fiskekaka", "synonyms": ["Fiskkaka", "Kärnkaka med fisk"], "preferences": {"A": 70, "B": 15, "C": 10, "D": 65}, "types": ["meal"]}, {"name": "Kebab med ris", "synonyms": ["<PERSON><PERSON><PERSON> och ris", "Ris med kebab"], "preferences": {"A": 80, "B": 80, "C": 30, "D": 85}, "types": ["meal"]}, {"name": "Biff med peppar<PERSON>s", "synonyms": ["<PERSON><PERSON><PERSON><PERSON><PERSON>", "B<PERSON> i pepparsås"], "preferences": {"A": 80, "B": 80, "C": 20, "D": 85}, "types": ["meal"]}, {"name": "Fläskkotletter med äppelmos", "synonyms": ["Kotletter med äppelmos", "Fläskkotletter"], "preferences": {"A": 80, "B": 80, "C": 30, "D": 85}, "types": ["meal"]}, {"name": "Pasta med pestosås", "synonyms": ["Pasta med pesto", "Pestosås med pasta"], "preferences": {"A": 80, "B": 80, "C": 20, "D": 90}, "types": ["meal"]}, {"name": "Krämig s<PERSON>mpstuvning", "synonyms": ["Svampstuvning", "Krämig svampgryta"], "preferences": {"A": 70, "B": 70, "C": 20, "D": 85}, "types": ["module"]}, {"name": "Risnudlar med gr<PERSON>nsaker", "synonyms": ["Nudelsallad", "Risnudlar med gr<PERSON>nsaker"], "preferences": {"A": 70, "B": 70, "C": 60, "D": 80}, "types": ["meal"]}, {"name": "Wok med tofu", "synonyms": ["Tofuwok", "Wok med tofu"], "preferences": {"A": 70, "B": 70, "C": 70, "D": 80}, "types": ["meal", "vegetarian"]}, {"name": "Miso-glaserad lax", "synonyms": ["Lax med misoglaze", "Miso-lax"], "preferences": {"A": 70, "B": 15, "C": 10, "D": 65}, "types": ["meal"]}, {"name": "Söt- och sur kyckling", "synonyms": ["Kyckling söt och sur", "Söt-sur kyckling"], "preferences": {"A": 80, "B": 80, "C": 25, "D": 95}, "types": ["meal"]}, {"name": "Friterad kyckling med dipsås", "synonyms": ["Kyckling med dipsås", "Friterad kyckling med sås"], "preferences": {"A": 80, "B": 80, "C": 20, "D": 90}, "types": ["meal"]}, {"name": "Baguette med rökt skinka", "synonyms": ["Skinkebaguette", "Baguette med skinka"], "preferences": {"A": 70, "B": 70, "C": 50, "D": 80}, "types": ["meal"]}, {"name": "Club sandwich", "synonyms": ["Trippel club sandwich", "Club sandwich"], "preferences": {"A": 70, "B": 70, "C": 50, "D": 80}, "types": ["meal"]}, {"name": "Grillad hall<PERSON><PERSON>", "synonyms": ["<PERSON><PERSON><PERSON>", "Grillad halloumiost"], "preferences": {"A": 70, "B": 70, "C": 60, "D": 85}, "types": ["meal", "vegetarian"]}, {"name": "Friterade zucchini-sticks", "synonyms": ["Zucchinisticks", "Friterade zucchinibitar"], "preferences": {"A": 70, "B": 70, "C": 70, "D": 80}, "types": ["module", "side", "vegetable"]}, {"name": "<PERSON><PERSON> sin carne", "synonyms": ["Vegetarisk chili", "<PERSON><PERSON> utan k<PERSON>"], "preferences": {"A": 75, "B": 75, "C": 70, "D": 80}, "types": ["meal", "vegetarian"]}, {"name": "Quiche med spenat", "synonyms": ["Spenatquiche", "Quiche med spenat"], "preferences": {"A": 75, "B": 75, "C": 60, "D": 80}, "types": ["meal"]}, {"name": "Falukorvssoppa", "synonyms": ["<PERSON><PERSON><PERSON><PERSON><PERSON>", "Falukorvssoppa"], "preferences": {"A": 70, "B": 70, "C": 30, "D": 80}, "types": ["meal"]}, {"name": "Köttfärspaj", "synonyms": ["<PERSON><PERSON> med k<PERSON>", "Köttfärspaj"], "preferences": {"A": 75, "B": 75, "C": 35, "D": 85}, "types": ["meal"]}, {"name": "Quinoabowl med gr<PERSON>nsaker", "synonyms": ["Quinoabowl", "Bowl med quinoa och g<PERSON><PERSON><PERSON><PERSON>"], "preferences": {"A": 75, "B": 75, "C": 65, "D": 80}, "types": ["meal"]}, {"name": "Bönburgare", "synonyms": ["Vegoburgare", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"], "preferences": {"A": 70, "B": 70, "C": 75, "D": 70}, "types": ["meal", "vegetarian", "protein"]}, {"name": "Grönsaksstavar med dipp", "synonyms": ["Grönsaksdipp", "Stavar med dipp"], "preferences": {"A": 80, "B": 80, "C": 70, "D": 80}, "types": ["module", "side", "vegetable"]}, {"name": "Spett med fläsk och grönsaker", "synonyms": ["Fläskspett", "<PERSON>pett med kött och grönsaker"], "preferences": {"A": 80, "B": 80, "C": 30, "D": 85}, "types": ["meal"]}, {"name": "Kycklingfilé med pestosås", "synonyms": ["Kyckling med pesto", "Filé med pestosås"], "preferences": {"A": 80, "B": 80, "C": 25, "D": 90}, "types": ["meal"]}, {"name": "<PERSON><PERSON> med ris", "synonyms": ["<PERSON><PERSON><PERSON><PERSON> med biff", "<PERSON><PERSON><PERSON><PERSON><PERSON>"], "preferences": {"A": 80, "B": 80, "C": 20, "D": 85}, "types": ["meal"]}, {"name": "Ugnsbakad lax med örter", "synonyms": ["Lax i ugn", "Bakad lax med örter"], "preferences": {"A": 70, "B": 15, "C": 10, "D": 65}, "types": ["meal"]}, {"name": "Gnocchi med pesto", "synonyms": ["<PERSON><PERSON><PERSON> gnocchi", "Gnocchi i pestosås"], "preferences": {"A": 75, "B": 75, "C": 30, "D": 85}, "types": ["meal"]}, {"name": "Vegetarisk moussaka", "synonyms": ["<PERSON><PERSON><PERSON> veget<PERSON>", "<PERSON><PERSON><PERSON><PERSON>"], "preferences": {"A": 75, "B": 75, "C": 65, "D": 80}, "types": ["meal", "vegetarian"]}, {"name": "<PERSON><PERSON><PERSON>", "synonyms": ["Champinjonfyllning", "<PERSON><PERSON><PERSON> s<PERSON>"], "preferences": {"A": 75, "B": 75, "C": 60, "D": 80}, "types": ["module", "side"]}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "synonyms": ["Zucchiniburgare", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"], "preferences": {"A": 65, "B": 65, "C": 70, "D": 60}, "types": ["module", "vegetarian", "protein"]}, {"name": "Halloumisallad", "synonyms": ["<PERSON><PERSON> med halloumi", "Grillad hall<PERSON><PERSON> sallad"], "preferences": {"A": 70, "B": 70, "C": 60, "D": 85}, "types": ["meal", "vegetarian"]}, {"name": "Köttgryta med rotfrukter", "synonyms": ["Rotfruktsgryta med kött", "Kött- och rotfruktsgryta"], "preferences": {"A": 75, "B": 75, "C": 30, "D": 80}, "types": ["meal"]}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "synonyms": ["<PERSON><PERSON><PERSON><PERSON><PERSON>", "Ungersk gulaschsoppa"], "preferences": {"A": 75, "B": 75, "C": 30, "D": 80}, "types": ["meal"]}, {"name": "Tacos med kyckling", "synonyms": ["Kycklingtacos", "Mexikanska kycklingtacos"], "preferences": {"A": 80, "B": 80, "C": 25, "D": 90}, "types": ["meal"]}, {"name": "<PERSON><PERSON><PERSON> fajitas", "synonyms": ["Fajitas med kyckling", "K<PERSON>cklingfajitas"], "preferences": {"A": 80, "B": 80, "C": 25, "D": 90}, "types": ["meal"]}, {"name": "Quinoa chili", "synonyms": ["Chili med quinoa", "<PERSON><PERSON><PERSON><PERSON><PERSON>"], "preferences": {"A": 75, "B": 75, "C": 70, "D": 80}, "types": ["meal", "vegetarian"]}, {"name": "Pasta Alfredo med kyckling", "synonyms": ["Alfredopasta med kyckling", "K<PERSON>ckling Alfredo"], "preferences": {"A": 80, "B": 80, "C": 20, "D": 90}, "types": ["meal"]}, {"name": "Vegetarisk pastagratäng", "synonyms": ["Pastagratäng vegetarisk", "<PERSON><PERSON><PERSON><PERSON>"], "preferences": {"A": 80, "B": 80, "C": 70, "D": 90}, "types": ["meal", "vegetarian"]}, {"name": "Fisk & skaldjurpaella", "synonyms": ["Paella med fisk", "Skaldjurpaella"], "preferences": {"A": 70, "B": 15, "C": 10, "D": 65}, "types": ["meal"]}, {"name": "Räkpasta med tomatsås", "synonyms": ["Pasta med räkor", "Räkpasta"], "preferences": {"A": 70, "B": 10, "C": 5, "D": 60}, "types": ["meal"]}, {"name": "Köttfärslimpa med potatismos", "synonyms": ["Limpa med köttfärs", "Köttfärslimpa"], "preferences": {"A": 75, "B": 75, "C": 30, "D": 80}, "types": ["meal"]}, {"name": "Ugnsbakad kyckling med grönsaker", "synonyms": ["Bakad kyckling med gr<PERSON>nsaker", "Ugnsrostad kyckling med grönsaker"], "preferences": {"A": 80, "B": 80, "C": 25, "D": 90}, "types": ["meal"]}, {"name": "Wok med grönsaker och nudlar", "synonyms": ["Nudelwok med grönsaker", "Wok med nudlar"], "preferences": {"A": 70, "B": 70, "C": 60, "D": 80}, "types": ["meal", "vegetarian"]}, {"name": "Grönsakspytt med tofu", "synonyms": ["Pytt med tofu", "<PERSON><PERSON><PERSON><PERSON>"], "preferences": {"A": 70, "B": 70, "C": 70, "D": 80}, "types": ["meal", "vegetarian"]}, {"name": "Kryddstark biffwok med grönsaker", "synonyms": ["Spicy beef stir-fry", "Kryddig biffwok"], "preferences": {"A": 80, "B": 80, "C": 20, "D": 90}, "types": ["meal"]}]