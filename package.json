{"private": true, "scripts": {"dev": "next dev", "build": "next build && npm run postbuild", "postbuild": "mkdir -p .next/standalone/.next && cp -r public .next/standalone/ && cp -r .next/static .next/standalone/.next/", "start": "node server.js", "start-local": "dotenv -e .env.production.local -- node .next/standalone/server.js", "migrate": "tsx scripts/migrate.ts", "test-db": "tsx scripts/test-connection.ts"}, "dependencies": {"@radix-ui/react-accordion": "^1.2.3", "@radix-ui/react-alert-dialog": "^1.1.6", "@radix-ui/react-aspect-ratio": "^1.1.2", "@radix-ui/react-avatar": "^1.1.3", "@radix-ui/react-checkbox": "^1.1.1", "@radix-ui/react-context-menu": "^2.2.6", "@radix-ui/react-dropdown-menu": "^2.1.1", "@radix-ui/react-icons": "^1.3.2", "@radix-ui/react-label": "^2.1.0", "@radix-ui/react-slot": "^1.1.0", "@radix-ui/react-tabs": "^1.1.3", "@supabase/ssr": "^0.6.1", "@supabase/supabase-js": "latest", "@types/mssql": "^9.1.7", "autoprefixer": "10.4.20", "class-variance-authority": "^0.7.0", "clsx": "^2.1.1", "cmdk": "^1.0.4", "dexie": "^4.0.11", "embla-carousel-react": "^8.5.2", "framer-motion": "^12.5.0", "fuse.js": "^7.1.0", "lucide-react": "^0.468.0", "mssql": "^11.0.1", "next-themes": "^0.2.1", "postcss": "^8", "prettier": "^3.3.3", "radix-ui": "^1.1.3", "react": "^18", "react-day-picker": "^9.5.1", "react-dnd": "^16.0.1", "react-dnd-html5-backend": "^16.0.1", "react-dom": "^18", "react-hook-form": "^7.54.2", "react-markdown": "^10.1.0", "react-resizable-panels": "^2.1.7", "stripe": "^17.6.0", "tailwind-merge": "^2", "tailwindcss": "^3", "tailwindcss-animate": "^1", "typescript": "^5", "vaul": "^1.1.2"}, "devDependencies": {"@types/node": "^20", "@types/react": "^18", "@types/react-dom": "^18", "dotenv-cli": "^8.0.0", "next": "^14.2.28", "tsx": "^4.20.3"}, "engines": {"node": ">=20.0.0", "npm": ">=9.0.0"}}